globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/pages/personal-center/DataOverview.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _user = __mako_require__("src/services/user.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            /**
 * 数据概览卡片组件
 *
 * 显示用户的个人统计数据，采用单行四列的水平布局。
 * 包括车辆、人员、预警、告警等指标的统计卡片。
 *
 * 主要功能：
 * 1. 显示车辆数量统计
 * 2. 显示人员数量统计
 * 3. 显示预警数量统计
 * 4. 显示告警数量统计
 *
 * 数据来源：
 * - 个人统计数据：通过UserService.getUserPersonalStats()获取
 *
 * 布局特点：
 * - 单行四列水平排列
 * - 每个统计项独立的卡片设计
 * - 响应式布局适配不同屏幕
 */ const DataOverview = ()=>{
                _s();
                // 定义内联样式对象
                const cardStyles = {
                    base: {
                        borderRadius: '8px',
                        border: '1px solid #d9d9d9',
                        height: '120px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: 'transparent'
                    },
                    vehicle: {
                    },
                    personnel: {
                    },
                    warning: {
                    },
                    alert: {
                    }
                };
                /**
   * 个人统计数据状态管理
   */ const [personalStats, setPersonalStats] = (0, _react.useState)({
                    vehicles: 0,
                    personnel: 0,
                    warnings: 0,
                    alerts: 0
                });
                const [statsLoading, setStatsLoading] = (0, _react.useState)(true);
                const [statsError, setStatsError] = (0, _react.useState)(null);
                // 获取统计数据
                (0, _react.useEffect)(()=>{
                    const fetchStatsData = async ()=>{
                        try {
                            const stats = await _user.UserService.getUserPersonalStats();
                            setPersonalStats(stats);
                            setStatsError(null);
                        } catch (error) {
                            console.error('获取统计数据失败:', error);
                            setStatsError('获取统计数据失败，请稍后重试');
                        } finally{
                            setStatsLoading(false);
                        }
                    };
                    fetchStatsData();
                }, []);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                    title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            display: 'flex',
                            alignItems: 'center',
                            gap: 8
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.BarChartOutlined, {
                                style: {
                                    fontSize: 16,
                                    color: '#1890ff'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                lineNumber: 104,
                                columnNumber: 11
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                children: "数据概览"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                lineNumber: 105,
                                columnNumber: 11
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/DataOverview.tsx",
                        lineNumber: 103,
                        columnNumber: 9
                    }, void 0),
                    style: {
                        marginBottom: 16,
                        borderRadius: 8,
                        border: '1px solid #d9d9d9'
                    },
                    headStyle: {
                        borderBottom: '1px solid #f0f0f0',
                        paddingBottom: 12
                    },
                    bodyStyle: {
                        padding: '20px'
                    },
                    children: statsError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                        message: "数据概览加载失败",
                        description: statsError,
                        type: "error",
                        showIcon: true,
                        style: {
                            borderRadius: 8
                        }
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/DataOverview.tsx",
                        lineNumber: 122,
                        columnNumber: 9
                    }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                        spinning: statsLoading,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                            gutter: [
                                16,
                                16
                            ],
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 12,
                                    sm: 6,
                                    md: 6,
                                    lg: 6,
                                    xl: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        style: {
                                            ...cardStyles.base,
                                            ...cardStyles.vehicle
                                        },
                                        styles: {
                                            body: {
                                                padding: '20px 16px',
                                                textAlign: 'center'
                                            }
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    marginBottom: 12
                                                },
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CarOutlined, {
                                                    style: {
                                                        fontSize: 24,
                                                        color: '#1890ff',
                                                        marginBottom: 8
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                                    lineNumber: 151,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 150,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    fontSize: 32,
                                                    fontWeight: 700,
                                                    color: '#1890ff',
                                                    lineHeight: 1,
                                                    marginBottom: 8
                                                },
                                                children: personalStats.vehicles
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 159,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    fontSize: 14,
                                                    color: '#1890ff',
                                                    fontWeight: 600,
                                                    opacity: 0.9
                                                },
                                                children: "车辆"
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 170,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 137,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 136,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 12,
                                    sm: 6,
                                    md: 6,
                                    lg: 6,
                                    xl: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        style: {
                                            ...cardStyles.base,
                                            ...cardStyles.personnel
                                        },
                                        styles: {
                                            body: {
                                                padding: '20px 16px',
                                                textAlign: 'center'
                                            }
                                        },
                                        hoverable: true,
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    marginBottom: 12
                                                },
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UsergroupAddOutlined, {
                                                    style: {
                                                        fontSize: 24,
                                                        color: '#52c41a',
                                                        marginBottom: 8
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                                    lineNumber: 199,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 198,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    fontSize: 32,
                                                    fontWeight: 700,
                                                    color: '#52c41a',
                                                    lineHeight: 1,
                                                    marginBottom: 8
                                                },
                                                children: personalStats.personnel
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 207,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    fontSize: 14,
                                                    color: '#52c41a',
                                                    fontWeight: 600,
                                                    opacity: 0.9
                                                },
                                                children: "人员"
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 218,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 185,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 184,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 12,
                                    sm: 6,
                                    md: 6,
                                    lg: 6,
                                    xl: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        style: {
                                            ...cardStyles.base,
                                            ...cardStyles.warning
                                        },
                                        styles: {
                                            body: {
                                                padding: '20px 16px',
                                                textAlign: 'center'
                                            }
                                        },
                                        hoverable: true,
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    marginBottom: 12
                                                },
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                                    style: {
                                                        fontSize: 24,
                                                        color: '#faad14',
                                                        marginBottom: 8
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                                    lineNumber: 247,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 246,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    fontSize: 32,
                                                    fontWeight: 700,
                                                    color: '#faad14',
                                                    lineHeight: 1,
                                                    marginBottom: 8
                                                },
                                                children: personalStats.warnings
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 255,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    fontSize: 14,
                                                    color: '#faad14',
                                                    fontWeight: 600,
                                                    opacity: 0.9
                                                },
                                                children: "预警"
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 266,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 233,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 232,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 12,
                                    sm: 6,
                                    md: 6,
                                    lg: 6,
                                    xl: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        style: {
                                            ...cardStyles.base,
                                            ...cardStyles.alert
                                        },
                                        styles: {
                                            body: {
                                                padding: '20px 16px',
                                                textAlign: 'center'
                                            }
                                        },
                                        hoverable: true,
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    marginBottom: 12
                                                },
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.AlertOutlined, {
                                                    style: {
                                                        fontSize: 24,
                                                        color: '#ff4d4f',
                                                        marginBottom: 8
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                                    lineNumber: 295,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 294,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    fontSize: 32,
                                                    fontWeight: 700,
                                                    color: '#ff4d4f',
                                                    lineHeight: 1,
                                                    marginBottom: 8
                                                },
                                                children: personalStats.alerts
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 303,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    fontSize: 14,
                                                    color: '#ff4d4f',
                                                    fontWeight: 600,
                                                    opacity: 0.9
                                                },
                                                children: "告警"
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 314,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 281,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 280,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/DataOverview.tsx",
                            lineNumber: 134,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/DataOverview.tsx",
                        lineNumber: 132,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/DataOverview.tsx",
                    lineNumber: 101,
                    columnNumber: 5
                }, this);
            };
            _s(DataOverview, "V/g64g42m1EsCCdbexpcUQrP3JA=");
            _c = DataOverview;
            var _default = DataOverview;
            var _c;
            $RefreshReg$(_c, "DataOverview");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '4325493700129169297';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/settings/index.tsx": [
            "p__settings__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.2509278067382730673.hot-update.js.map