{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.6893338795988470837.hot-update.js", "src/pages/personal-center/DataOverview.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='3127013319058785637';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import {\n  BarChartOutlined,\n  CarOutlined,\n  UsergroupAddOutlined,\n  ExclamationCircleOutlined,\n  AlertOutlined,\n} from '@ant-design/icons';\nimport {\n  Alert,\n  Card,\n  Col,\n  Row,\n  Spin,\n} from 'antd';\nimport { ProCard } from '@ant-design/pro-components';\nimport React, { useEffect, useState } from 'react';\nimport { UserService } from '@/services/user';\nimport type { UserPersonalStatsResponse } from '@/types/api';\n\n/**\n * 数据概览卡片组件\n *\n * 显示用户的个人统计数据，采用单行四列的水平布局。\n * 包括车辆、人员、预警、告警等指标的统计卡片。\n *\n * 主要功能：\n * 1. 显示车辆数量统计\n * 2. 显示人员数量统计\n * 3. 显示预警数量统计\n * 4. 显示告警数量统计\n *\n * 数据来源：\n * - 个人统计数据：通过UserService.getUserPersonalStats()获取\n *\n * 布局特点：\n * - 单行四列水平排列\n * - 每个统计项独立的卡片设计\n * - 响应式布局适配不同屏幕\n */\nconst DataOverview: React.FC = () => {\n  // 定义内联样式对象\n  const cardStyles = {\n    base: {\n      borderRadius: '8px',\n      border: '1px solid #d9d9d9',\n      height: '120px',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      backgroundColor: 'transparent', // 移除背景色\n    },\n    vehicle: {\n      // backgroundColor: '#1890ff', // 移除背景色\n      // color: 'white', // 移除白色文字\n    },\n    personnel: {\n      // backgroundColor: '#52c41a', // 移除背景色\n      // color: 'white', // 移除白色文字\n    },\n    warning: {\n      // backgroundColor: '#faad14', // 移除背景色\n      // color: 'white', // 移除白色文字\n    },\n    alert: {\n      // backgroundColor: '#f5222d', // 移除背景色\n      // color: 'white', // 移除白色文字\n    },\n  };\n  /**\n   * 个人统计数据状态管理\n   */\n  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({\n    vehicles: 0,\n    personnel: 0,\n    warnings: 0,\n    alerts: 0,\n  });\n\n  const [statsLoading, setStatsLoading] = useState(true);\n  const [statsError, setStatsError] = useState<string | null>(null);\n\n  // Modal状态管理\n  const [settingsModalVisible, setSettingsModalVisible] = useState(false);\n\n  // 获取统计数据\n  useEffect(() => {\n    const fetchStatsData = async () => {\n      try {\n        const stats = await UserService.getUserPersonalStats();\n        setPersonalStats(stats);\n        setStatsError(null);\n      } catch (error) {\n        console.error('获取统计数据失败:', error);\n        setStatsError('获取统计数据失败，请稍后重试');\n      } finally {\n        setStatsLoading(false);\n      }\n    };\n\n    fetchStatsData();\n  }, []);\n\n  return (\n    <>\n      <ProCard\n        title={\n          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>\n            <BarChartOutlined style={{ fontSize: 16, color: '#1890ff' }} />\n            <span>数据概览</span>\n          </div>\n        }\n        extra={\n          <Space size={16}>\n            <UserInfoPopover userInfo={userInfo}>\n              <QuestionCircleOutlined\n                style={{\n                  fontSize: 18,\n                  color: '#8c8c8c',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease',\n                  padding: '4px',\n                  borderRadius: '50%',\n                  background: 'transparent',\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.color = '#1890ff';\n                  e.currentTarget.style.background = 'rgba(24, 144, 255, 0.08)';\n                  e.currentTarget.style.transform = 'scale(1.1)';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.color = '#8c8c8c';\n                  e.currentTarget.style.background = 'transparent';\n                  e.currentTarget.style.transform = 'scale(1)';\n                }}\n              />\n            </UserInfoPopover>\n            <SettingOutlined\n              style={{\n                fontSize: 18,\n                color: '#8c8c8c',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease',\n                padding: '4px',\n                borderRadius: '50%',\n                background: 'transparent',\n              }}\n              onMouseEnter={(e) => {\n                e.currentTarget.style.color = '#1890ff';\n                e.currentTarget.style.background = 'rgba(24, 144, 255, 0.08)';\n                e.currentTarget.style.transform = 'scale(1.1)';\n              }}\n              onMouseLeave={(e) => {\n                e.currentTarget.style.color = '#8c8c8c';\n                e.currentTarget.style.background = 'transparent';\n                e.currentTarget.style.transform = 'scale(1)';\n              }}\n              onClick={() => setSettingsModalVisible(true)}\n            />\n          </Space>\n        }\n        style={{\n          marginBottom: 16,\n          borderRadius: 8,\n          border: '1px solid #d9d9d9',\n        }}\n        headStyle={{\n          borderBottom: '1px solid #f0f0f0',\n          paddingBottom: 12,\n        }}\n        bodyStyle={{\n          padding: '20px',\n        }}\n      >\n      {statsError ? (\n        <Alert\n          message=\"数据概览加载失败\"\n          description={statsError}\n          type=\"error\"\n          showIcon\n          style={{\n            borderRadius: 8,\n          }}\n        />\n      ) : (\n        <Spin spinning={statsLoading}>\n          {/* 单行四列布局 */}\n          <Row gutter={[16, 16]}>\n            {/* 车辆统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <Card\n                style={{\n                  ...cardStyles.base,\n                  ...cardStyles.vehicle,\n                }}\n                styles={{\n                  body: {\n                    padding: '20px 16px',\n                    textAlign: 'center',\n                  },\n                }}\n              >\n                <div style={{ marginBottom: 12 }}>\n                  <CarOutlined\n                    style={{\n                      fontSize: 24,\n                      color: '#1890ff',\n                      marginBottom: 8,\n                    }}\n                  />\n                </div>\n                <div\n                  style={{\n                    fontSize: 32,\n                    fontWeight: 700,\n                    color: '#1890ff',\n                    lineHeight: 1,\n                    marginBottom: 8,\n                  }}\n                >\n                  {personalStats.vehicles}\n                </div>\n                <div\n                  style={{\n                    fontSize: 14,\n                    color: '#1890ff',\n                    fontWeight: 600,\n                    opacity: 0.9, // 提高透明度以确保可读性\n                  }}\n                >\n                  车辆\n                </div>\n              </Card>\n            </Col>\n\n            {/* 人员统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <Card\n                style={{\n                  ...cardStyles.base,\n                  ...cardStyles.personnel,\n                }}\n                styles={{\n                  body: {\n                    padding: '20px 16px',\n                    textAlign: 'center',\n                  },\n                }}\n              >\n                <div style={{ marginBottom: 12 }}>\n                  <UsergroupAddOutlined\n                    style={{\n                      fontSize: 24,\n                      color: '#52c41a',\n                      marginBottom: 8,\n                    }}\n                  />\n                </div>\n                <div\n                  style={{\n                    fontSize: 32,\n                    fontWeight: 700,\n                    color: '#52c41a',\n                    lineHeight: 1,\n                    marginBottom: 8,\n                  }}\n                >\n                  {personalStats.personnel}\n                </div>\n                <div\n                  style={{\n                    fontSize: 14,\n                    color: '#52c41a',\n                    fontWeight: 600,\n                    opacity: 0.9, // 提高透明度以确保可读性\n                  }}\n                >\n                  人员\n                </div>\n              </Card>\n            </Col>\n\n            {/* 预警统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <Card\n                style={{\n                  ...cardStyles.base,\n                  ...cardStyles.warning,\n                }}\n                styles={{\n                  body: {\n                    padding: '20px 16px',\n                    textAlign: 'center',\n                  },\n                }}\n              >\n                <div style={{ marginBottom: 12 }}>\n                  <ExclamationCircleOutlined\n                    style={{\n                      fontSize: 24,\n                      color: '#faad14',\n                      marginBottom: 8,\n                    }}\n                  />\n                </div>\n                <div\n                  style={{\n                    fontSize: 32,\n                    fontWeight: 700,\n                    color: '#faad14',\n                    lineHeight: 1,\n                    marginBottom: 8,\n                  }}\n                >\n                  {personalStats.warnings}\n                </div>\n                <div\n                  style={{\n                    fontSize: 14,\n                    color: '#faad14',\n                    fontWeight: 600,\n                    opacity: 0.9, // 提高透明度以确保可读性\n                  }}\n                >\n                  预警\n                </div>\n              </Card>\n            </Col>\n\n            {/* 告警统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <Card\n                style={{\n                  ...cardStyles.base,\n                  ...cardStyles.alert,\n                }}\n                styles={{\n                  body: {\n                    padding: '20px 16px',\n                    textAlign: 'center',\n                  },\n                }}\n              >\n                <div style={{ marginBottom: 12 }}>\n                  <AlertOutlined\n                    style={{\n                      fontSize: 24,\n                      color: '#ff4d4f',\n                      marginBottom: 8,\n                    }}\n                  />\n                </div>\n                <div\n                  style={{\n                    fontSize: 32,\n                    fontWeight: 700,\n                    color: '#ff4d4f',\n                    lineHeight: 1,\n                    marginBottom: 8,\n                  }}\n                >\n                  {personalStats.alerts}\n                </div>\n                <div\n                  style={{\n                    fontSize: 14,\n                    color: '#ff4d4f',\n                    fontWeight: 600,\n                    opacity: 0.9, // 提高透明度以确保可读性\n                  }}\n                >\n                  告警\n                </div>\n              </Card>\n            </Col>\n          </Row>\n        </Spin>\n      )}\n      </ProCard>\n\n      {/* 统一设置Modal */}\n      <UnifiedSettingsModal\n        visible={settingsModalVisible}\n        onCancel={() => setSettingsModalVisible(false)}\n        userInfo={userInfo}\n        onSuccess={() => {\n          // 可以在这里刷新用户信息\n          console.log('设置操作成功');\n        }}\n      />\n    </>\n  );\n};\n\nexport default DataOverview;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCCsYb;;;2BAAA;;;;;;0CAnYO;yCAOA;kDACiB;oFACmB;yCACf;;;;;;;;;;YAG5B;;;;;;;;;;;;;;;;;;;CAmBC,GACD,MAAM,eAAyB;;gBAC7B,WAAW;gBACX,MAAM,aAAa;oBACjB,MAAM;wBACJ,cAAc;wBACd,QAAQ;wBACR,QAAQ;wBACR,SAAS;wBACT,YAAY;wBACZ,gBAAgB;wBAChB,iBAAiB;oBACnB;oBACA,SAAS;oBAGT;oBACA,WAAW;oBAGX;oBACA,SAAS;oBAGT;oBACA,OAAO;oBAGP;gBACF;gBACA;;GAEC,GACD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAA4B;oBAC5E,UAAU;oBACV,WAAW;oBACX,UAAU;oBACV,QAAQ;gBACV;gBAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAC;gBACjD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAgB;gBAE5D,YAAY;gBACZ,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,IAAA,eAAQ,EAAC;gBAEjE,SAAS;gBACT,IAAA,gBAAS,EAAC;oBACR,MAAM,iBAAiB;wBACrB,IAAI;4BACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,oBAAoB;4BACpD,iBAAiB;4BACjB,cAAc;wBAChB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,aAAa;4BAC3B,cAAc;wBAChB,SAAU;4BACR,gBAAgB;wBAClB;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,qBACE;;sCACE,2BAAC,sBAAO;4BACN,qBACE,2BAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,YAAY;oCAAU,KAAK;gCAAE;;kDAC1D,2BAAC,uBAAgB;wCAAC,OAAO;4CAAE,UAAU;4CAAI,OAAO;wCAAU;;;;;;kDAC1D,2BAAC;kDAAK;;;;;;;;;;;;4BAGV,qBACE,2BAAC;gCAAM,MAAM;;kDACX,2BAAC;wCAAgB,UAAU;kDACzB,cAAA,2BAAC;4CACC,OAAO;gDACL,UAAU;gDACV,OAAO;gDACP,QAAQ;gDACR,YAAY;gDACZ,SAAS;gDACT,cAAc;gDACd,YAAY;4CACd;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;gDAC9B,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;gDACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4CACpC;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;gDAC9B,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;gDACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4CACpC;;;;;;;;;;;kDAGJ,2BAAC;wCACC,OAAO;4CACL,UAAU;4CACV,OAAO;4CACP,QAAQ;4CACR,YAAY;4CACZ,SAAS;4CACT,cAAc;4CACd,YAAY;wCACd;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;4CAC9B,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;4CACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wCACpC;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;4CAC9B,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;4CACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wCACpC;wCACA,SAAS,IAAM,wBAAwB;;;;;;;;;;;;4BAI7C,OAAO;gCACL,cAAc;gCACd,cAAc;gCACd,QAAQ;4BACV;4BACA,WAAW;gCACT,cAAc;gCACd,eAAe;4BACjB;4BACA,WAAW;gCACT,SAAS;4BACX;sCAED,2BACC,2BAAC,WAAK;gCACJ,SAAQ;gCACR,aAAa;gCACb,MAAK;gCACL,QAAQ;gCACR,OAAO;oCACL,cAAc;gCAChB;;;;;qDAGF,2BAAC,UAAI;gCAAC,UAAU;0CAEd,cAAA,2BAAC,SAAG;oCAAC,QAAQ;wCAAC;wCAAI;qCAAG;;sDAEnB,2BAAC,SAAG;4CAAC,IAAI;4CAAI,IAAI;4CAAG,IAAI;4CAAG,IAAI;4CAAG,IAAI;sDACpC,cAAA,2BAAC,UAAI;gDACH,OAAO;oDACL,GAAG,WAAW,IAAI;oDAClB,GAAG,WAAW,OAAO;gDACvB;gDACA,QAAQ;oDACN,MAAM;wDACJ,SAAS;wDACT,WAAW;oDACb;gDACF;;kEAEA,2BAAC;wDAAI,OAAO;4DAAE,cAAc;wDAAG;kEAC7B,cAAA,2BAAC,kBAAW;4DACV,OAAO;gEACL,UAAU;gEACV,OAAO;gEACP,cAAc;4DAChB;;;;;;;;;;;kEAGJ,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,YAAY;4DACZ,OAAO;4DACP,YAAY;4DACZ,cAAc;wDAChB;kEAEC,cAAc,QAAQ;;;;;;kEAEzB,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,OAAO;4DACP,YAAY;4DACZ,SAAS;wDACX;kEACD;;;;;;;;;;;;;;;;;sDAOL,2BAAC,SAAG;4CAAC,IAAI;4CAAI,IAAI;4CAAG,IAAI;4CAAG,IAAI;4CAAG,IAAI;sDACpC,cAAA,2BAAC,UAAI;gDACH,OAAO;oDACL,GAAG,WAAW,IAAI;oDAClB,GAAG,WAAW,SAAS;gDACzB;gDACA,QAAQ;oDACN,MAAM;wDACJ,SAAS;wDACT,WAAW;oDACb;gDACF;;kEAEA,2BAAC;wDAAI,OAAO;4DAAE,cAAc;wDAAG;kEAC7B,cAAA,2BAAC,2BAAoB;4DACnB,OAAO;gEACL,UAAU;gEACV,OAAO;gEACP,cAAc;4DAChB;;;;;;;;;;;kEAGJ,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,YAAY;4DACZ,OAAO;4DACP,YAAY;4DACZ,cAAc;wDAChB;kEAEC,cAAc,SAAS;;;;;;kEAE1B,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,OAAO;4DACP,YAAY;4DACZ,SAAS;wDACX;kEACD;;;;;;;;;;;;;;;;;sDAOL,2BAAC,SAAG;4CAAC,IAAI;4CAAI,IAAI;4CAAG,IAAI;4CAAG,IAAI;4CAAG,IAAI;sDACpC,cAAA,2BAAC,UAAI;gDACH,OAAO;oDACL,GAAG,WAAW,IAAI;oDAClB,GAAG,WAAW,OAAO;gDACvB;gDACA,QAAQ;oDACN,MAAM;wDACJ,SAAS;wDACT,WAAW;oDACb;gDACF;;kEAEA,2BAAC;wDAAI,OAAO;4DAAE,cAAc;wDAAG;kEAC7B,cAAA,2BAAC,gCAAyB;4DACxB,OAAO;gEACL,UAAU;gEACV,OAAO;gEACP,cAAc;4DAChB;;;;;;;;;;;kEAGJ,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,YAAY;4DACZ,OAAO;4DACP,YAAY;4DACZ,cAAc;wDAChB;kEAEC,cAAc,QAAQ;;;;;;kEAEzB,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,OAAO;4DACP,YAAY;4DACZ,SAAS;wDACX;kEACD;;;;;;;;;;;;;;;;;sDAOL,2BAAC,SAAG;4CAAC,IAAI;4CAAI,IAAI;4CAAG,IAAI;4CAAG,IAAI;4CAAG,IAAI;sDACpC,cAAA,2BAAC,UAAI;gDACH,OAAO;oDACL,GAAG,WAAW,IAAI;oDAClB,GAAG,WAAW,KAAK;gDACrB;gDACA,QAAQ;oDACN,MAAM;wDACJ,SAAS;wDACT,WAAW;oDACb;gDACF;;kEAEA,2BAAC;wDAAI,OAAO;4DAAE,cAAc;wDAAG;kEAC7B,cAAA,2BAAC,oBAAa;4DACZ,OAAO;gEACL,UAAU;gEACV,OAAO;gEACP,cAAc;4DAChB;;;;;;;;;;;kEAGJ,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,YAAY;4DACZ,OAAO;4DACP,YAAY;4DACZ,cAAc;wDAChB;kEAEC,cAAc,MAAM;;;;;;kEAEvB,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,OAAO;4DACP,YAAY;4DACZ,SAAS;wDACX;kEACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAWX,2BAAC;4BACC,SAAS;4BACT,UAAU,IAAM,wBAAwB;4BACxC,UAAU;4BACV,WAAW;gCACT,cAAc;gCACd,QAAQ,GAAG,CAAC;4BACd;;;;;;;;YAIR;eAhWM;iBAAA;gBAkWN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDtYD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACrjB"}