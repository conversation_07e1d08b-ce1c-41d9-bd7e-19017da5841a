{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.7192571193724637821.hot-update.js", "src/pages/personal-center/PersonalInfo.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='10838164021736474733';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import {\n  Alert,\n  Spin,\n  Typography,\n} from 'antd';\nimport { ProCard } from '@ant-design/pro-components';\n\nimport React, { useEffect, useState } from 'react';\nimport { UserService } from '@/services/user';\nimport type { UserProfileDetailResponse } from '@/types/api';\n\nconst { Title, Text } = Typography;\n\n/**\n * 个人信息组件\n *\n * 显示用户的基本个人信息，采用简洁的卡片设计。\n * 用户名支持悬浮显示详细信息（电话、邮箱、注册时间等）。\n *\n * 主要功能：\n * 1. 显示用户头像（使用姓名首字母）\n * 2. 显示用户姓名（支持气泡卡片显示详细信息）\n * 3. 显示最后登录时间和登录团队\n *\n * 数据来源：\n * - 用户详细信息：通过UserService.getUserProfileDetail()获取\n */\n\ninterface PersonalInfoProps {\n  onUserInfoChange?: (userInfo: UserProfileDetailResponse) => void;\n}\n\nconst PersonalInfo: React.FC<PersonalInfoProps> = ({ onUserInfoChange }) => {\n  /**\n   * 用户详细信息状态管理\n   */\n  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({\n    name: '',\n    position: '',\n    email: '',\n    telephone: '',\n    registerDate: '',\n    lastLoginTime: '',\n    lastLoginTeam: '',\n    teamCount: 0,\n    avatar: '',\n  });\n\n  const [userInfoLoading, setUserInfoLoading] = useState(true);\n  const [userInfoError, setUserInfoError] = useState<string | null>(null);\n\n  // 获取用户数据\n  useEffect(() => {\n    const fetchUserData = async () => {\n      try {\n        const userDetail = await UserService.getUserProfileDetail();\n        setUserInfo(userDetail);\n        setUserInfoError(null);\n        // 通知父组件用户信息已更新\n        onUserInfoChange?.(userDetail);\n      } catch (error) {\n        console.error('获取用户详细信息失败:', error);\n        setUserInfoError('获取用户详细信息失败，请稍后重试');\n      } finally {\n        setUserInfoLoading(false);\n      }\n    };\n\n    fetchUserData();\n  }, [onUserInfoChange]);\n\n  return (\n    <ProCard\n      style={{\n        marginBottom: 16,\n        borderRadius: 8,\n        border: '1px solid #d9d9d9',\n      }}\n    >\n      {userInfoError ? (\n        <Alert\n          message=\"个人信息加载失败\"\n          description={userInfoError}\n          type=\"error\"\n          showIcon\n          style={{\n            borderRadius: 12,\n            border: 'none',\n          }}\n        />\n      ) : (\n        <Spin spinning={userInfoLoading}>\n          {/* 问候区域 */}\n          <div style={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            padding: '16px 0'\n          }}>\n            {/* 问候语 */}\n            <Typography.Title\n              level={3}\n              style={{\n                margin: 0,\n                fontSize: 20,\n                color: '#262626',\n              }}\n            >\n              您好，{userInfo.name || '加载中...'}\n            </Typography.Title>\n          </div>\n        </Spin>\n      )}\n    </ProCard>\n  );\n};\n\nexport default PersonalInfo;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCCkHb;;;2BAAA;;;;;;yCAjHO;kDACiB;oFAEmB;yCACf;;;;;;;;;;YAG5B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAqBlC,MAAM,eAA4C,CAAC,EAAE,gBAAgB,EAAE;;gBACrE;;GAEC,GACD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAA4B;oBAClE,MAAM;oBACN,UAAU;oBACV,OAAO;oBACP,WAAW;oBACX,cAAc;oBACd,eAAe;oBACf,eAAe;oBACf,WAAW;oBACX,QAAQ;gBACV;gBAEA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAC;gBACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;gBAElE,SAAS;gBACT,IAAA,gBAAS,EAAC;oBACR,MAAM,gBAAgB;wBACpB,IAAI;4BACF,MAAM,aAAa,MAAM,iBAAW,CAAC,oBAAoB;4BACzD,YAAY;4BACZ,iBAAiB;4BACjB,eAAe;4BACf,6BAAA,+BAAA,iBAAmB;wBACrB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,eAAe;4BAC7B,iBAAiB;wBACnB,SAAU;4BACR,mBAAmB;wBACrB;oBACF;oBAEA;gBACF,GAAG;oBAAC;iBAAiB;gBAErB,qBACE,2BAAC,sBAAO;oBACN,OAAO;wBACL,cAAc;wBACd,cAAc;wBACd,QAAQ;oBACV;8BAEC,8BACC,2BAAC,WAAK;wBACJ,SAAQ;wBACR,aAAa;wBACb,MAAK;wBACL,QAAQ;wBACR,OAAO;4BACL,cAAc;4BACd,QAAQ;wBACV;;;;;6CAGF,2BAAC,UAAI;wBAAC,UAAU;kCAEd,cAAA,2BAAC;4BAAI,OAAO;gCACV,SAAS;gCACT,gBAAgB;gCAChB,YAAY;gCACZ,SAAS;4BACX;sCAEE,cAAA,2BAAC,gBAAU,CAAC,KAAK;gCACf,OAAO;gCACP,OAAO;oCACL,QAAQ;oCACR,UAAU;oCACV,OAAO;gCACT;;oCACD;oCACK,SAAS,IAAI,IAAI;;;;;;;;;;;;;;;;;;;;;;YAOnC;eAnFM;iBAAA;gBAqFN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDlHD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACrjB"}