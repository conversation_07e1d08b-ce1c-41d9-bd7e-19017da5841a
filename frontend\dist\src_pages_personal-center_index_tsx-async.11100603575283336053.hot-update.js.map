{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.11100603575283336053.hot-update.js", "src/pages/personal-center/DataOverview.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='3574675759870840192';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import {\n  BarChartOutlined,\n  CarOutlined,\n  UsergroupAddOutlined,\n  ExclamationCircleOutlined,\n  AlertOutlined,\n} from '@ant-design/icons';\nimport {\n  Alert,\n  Spin,\n  theme,\n} from 'antd';\nimport { ProCard, StatisticCard } from '@ant-design/pro-components';\nimport React, { useEffect, useState } from 'react';\nimport { UserService } from '@/services/user';\nimport type { UserPersonalStatsResponse } from '@/types/api';\n\n/**\n * 数据概览卡片组件\n *\n * 使用 Ant Design Pro Components 的 StatisticCard 组件显示用户的个人统计数据，\n * 采用单行四列的响应式网格布局。包括车辆、人员、预警、告警等指标的统计卡片。\n *\n * 主要功能：\n * 1. 显示车辆数量统计 - 使用车辆图标，蓝色主题\n * 2. 显示人员数量统计 - 使用用户组图标，绿色主题\n * 3. 显示预警数量统计 - 使用感叹号图标，橙色主题\n * 4. 显示告警数量统计 - 使用警告图标，红色主题\n *\n * 数据来源：\n * - 个人统计数据：通过UserService.getUserPersonalStats()获取\n *\n * 布局特点：\n * - 使用 StatisticCard 组件提供专业的数据展示\n * - 单行四列水平排列，响应式布局适配不同屏幕\n * - 每个统计项都有语义化的图标和颜色主题\n * - 统一的卡片样式和高度\n */\nconst DataOverview: React.FC = () => {\n  /**\n   * 个人统计数据状态管理\n   */\n  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({\n    vehicles: 0,\n    personnel: 0,\n    warnings: 0,\n    alerts: 0,\n  });\n\n  const [statsLoading, setStatsLoading] = useState(true);\n  const [statsError, setStatsError] = useState<string | null>(null);\n\n  // 获取统计数据\n  useEffect(() => {\n    const fetchStatsData = async () => {\n      try {\n        const stats = await UserService.getUserPersonalStats();\n        setPersonalStats(stats);\n        setStatsError(null);\n      } catch (error) {\n        console.error('获取统计数据失败:', error);\n        setStatsError('获取统计数据失败，请稍后重试');\n      } finally {\n        setStatsLoading(false);\n      }\n    };\n\n    fetchStatsData();\n  }, []);\n\n  return (\n    <ProCard\n      title={\n        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>\n          <BarChartOutlined style={{ fontSize: 16, color: '#1890ff' }} />\n          <span>数据概览</span>\n        </div>\n      }\n      style={{\n        marginBottom: 16,\n        borderRadius: 8,\n        border: '1px solid #d9d9d9',\n      }}\n      headStyle={{\n        borderBottom: '1px solid #f0f0f0',\n        paddingBottom: 12,\n      }}\n      bodyStyle={{\n        padding: '20px',\n      }}\n    >\n      {statsError ? (\n        <Alert\n          message=\"数据概览加载失败\"\n          description={statsError}\n          type=\"error\"\n          showIcon\n          style={{\n            borderRadius: 8,\n          }}\n        />\n      ) : (\n        <Spin spinning={statsLoading}>\n          {/* 使用 StatisticCard 组件的响应式网格布局 */}\n          <Row gutter={[16, 16]}>\n            {/* 车辆统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <StatisticCard\n                statistic={{\n                  title: '车辆',\n                  value: personalStats.vehicles,\n                  icon: <CarOutlined style={{ color: '#1890ff' }} />,\n                  valueStyle: {\n                    color: '#1890ff',\n                    fontSize: 32,\n                    fontWeight: 700,\n                  },\n                }}\n                style={{\n                  borderRadius: 8,\n                  border: '1px solid #d9d9d9',\n                  height: 120,\n                }}\n              />\n            </Col>\n\n            {/* 人员统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <StatisticCard\n                statistic={{\n                  title: '人员',\n                  value: personalStats.personnel,\n                  icon: <UsergroupAddOutlined style={{ color: '#52c41a' }} />,\n                  valueStyle: {\n                    color: '#52c41a',\n                    fontSize: 32,\n                    fontWeight: 700,\n                  },\n                }}\n                style={{\n                  borderRadius: 8,\n                  border: '1px solid #d9d9d9',\n                  height: 120,\n                }}\n              />\n            </Col>\n\n            {/* 预警统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <StatisticCard\n                statistic={{\n                  title: '预警',\n                  value: personalStats.warnings,\n                  icon: <ExclamationCircleOutlined style={{ color: '#faad14' }} />,\n                  valueStyle: {\n                    color: '#faad14',\n                    fontSize: 32,\n                    fontWeight: 700,\n                  },\n                }}\n                style={{\n                  borderRadius: 8,\n                  border: '1px solid #d9d9d9',\n                  height: 120,\n                }}\n              />\n            </Col>\n\n            {/* 告警统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <StatisticCard\n                statistic={{\n                  title: '告警',\n                  value: personalStats.alerts,\n                  icon: <AlertOutlined style={{ color: '#ff4d4f' }} />,\n                  valueStyle: {\n                    color: '#ff4d4f',\n                    fontSize: 32,\n                    fontWeight: 700,\n                  },\n                }}\n                style={{\n                  borderRadius: 8,\n                  border: '1px solid #d9d9d9',\n                  height: 120,\n                }}\n              />\n            </Col>\n          </Row>\n        </Spin>\n      )}\n    </ProCard>\n  );\n};\n\nexport default DataOverview;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCCgMb;;;2BAAA;;;;;;0CA7LO;yCAKA;kDACgC;oFACI;yCACf;;;;;;;;;;YAG5B;;;;;;;;;;;;;;;;;;;;CAoBC,GACD,MAAM,eAAyB;;gBAC7B;;GAEC,GACD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAA4B;oBAC5E,UAAU;oBACV,WAAW;oBACX,UAAU;oBACV,QAAQ;gBACV;gBAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAC;gBACjD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAgB;gBAE5D,SAAS;gBACT,IAAA,gBAAS,EAAC;oBACR,MAAM,iBAAiB;wBACrB,IAAI;4BACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,oBAAoB;4BACpD,iBAAiB;4BACjB,cAAc;wBAChB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,aAAa;4BAC3B,cAAc;wBAChB,SAAU;4BACR,gBAAgB;wBAClB;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,qBACE,2BAAC,sBAAO;oBACN,qBACE,2BAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,YAAY;4BAAU,KAAK;wBAAE;;0CAC1D,2BAAC,uBAAgB;gCAAC,OAAO;oCAAE,UAAU;oCAAI,OAAO;gCAAU;;;;;;0CAC1D,2BAAC;0CAAK;;;;;;;;;;;;oBAGV,OAAO;wBACL,cAAc;wBACd,cAAc;wBACd,QAAQ;oBACV;oBACA,WAAW;wBACT,cAAc;wBACd,eAAe;oBACjB;oBACA,WAAW;wBACT,SAAS;oBACX;8BAEC,2BACC,2BAAC,WAAK;wBACJ,SAAQ;wBACR,aAAa;wBACb,MAAK;wBACL,QAAQ;wBACR,OAAO;4BACL,cAAc;wBAChB;;;;;6CAGF,2BAAC,UAAI;wBAAC,UAAU;kCAEd,cAAA,2BAAC;4BAAI,QAAQ;gCAAC;gCAAI;6BAAG;;8CAEnB,2BAAC;oCAAI,IAAI;oCAAI,IAAI;oCAAG,IAAI;oCAAG,IAAI;oCAAG,IAAI;8CACpC,cAAA,2BAAC,4BAAa;wCACZ,WAAW;4CACT,OAAO;4CACP,OAAO,cAAc,QAAQ;4CAC7B,oBAAM,2BAAC,kBAAW;gDAAC,OAAO;oDAAE,OAAO;gDAAU;;;;;;4CAC7C,YAAY;gDACV,OAAO;gDACP,UAAU;gDACV,YAAY;4CACd;wCACF;wCACA,OAAO;4CACL,cAAc;4CACd,QAAQ;4CACR,QAAQ;wCACV;;;;;;;;;;;8CAKJ,2BAAC;oCAAI,IAAI;oCAAI,IAAI;oCAAG,IAAI;oCAAG,IAAI;oCAAG,IAAI;8CACpC,cAAA,2BAAC,4BAAa;wCACZ,WAAW;4CACT,OAAO;4CACP,OAAO,cAAc,SAAS;4CAC9B,oBAAM,2BAAC,2BAAoB;gDAAC,OAAO;oDAAE,OAAO;gDAAU;;;;;;4CACtD,YAAY;gDACV,OAAO;gDACP,UAAU;gDACV,YAAY;4CACd;wCACF;wCACA,OAAO;4CACL,cAAc;4CACd,QAAQ;4CACR,QAAQ;wCACV;;;;;;;;;;;8CAKJ,2BAAC;oCAAI,IAAI;oCAAI,IAAI;oCAAG,IAAI;oCAAG,IAAI;oCAAG,IAAI;8CACpC,cAAA,2BAAC,4BAAa;wCACZ,WAAW;4CACT,OAAO;4CACP,OAAO,cAAc,QAAQ;4CAC7B,oBAAM,2BAAC,gCAAyB;gDAAC,OAAO;oDAAE,OAAO;gDAAU;;;;;;4CAC3D,YAAY;gDACV,OAAO;gDACP,UAAU;gDACV,YAAY;4CACd;wCACF;wCACA,OAAO;4CACL,cAAc;4CACd,QAAQ;4CACR,QAAQ;wCACV;;;;;;;;;;;8CAKJ,2BAAC;oCAAI,IAAI;oCAAI,IAAI;oCAAG,IAAI;oCAAG,IAAI;oCAAG,IAAI;8CACpC,cAAA,2BAAC,4BAAa;wCACZ,WAAW;4CACT,OAAO;4CACP,OAAO,cAAc,MAAM;4CAC3B,oBAAM,2BAAC,oBAAa;gDAAC,OAAO;oDAAE,OAAO;gDAAU;;;;;;4CAC/C,YAAY;gDACV,OAAO;gDACP,UAAU;gDACV,YAAY;4CACd;wCACF;wCACA,OAAO;4CACL,cAAc;4CACd,QAAQ;4CACR,QAAQ;wCACV;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQhB;eA3JM;iBAAA;gBA6JN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDhMD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACrjB"}