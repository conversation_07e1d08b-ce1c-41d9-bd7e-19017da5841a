{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.8732603064738034269.hot-update.js", "src/pages/personal-center/DataOverview.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='6016937623220062194';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import {\n  BarChartOutlined,\n  CarOutlined,\n  UsergroupAddOutlined,\n  ExclamationCircleOutlined,\n  AlertOutlined,\n  QuestionCircleOutlined,\n  SettingOutlined,\n} from '@ant-design/icons';\nimport {\n  Alert,\n  Card,\n  Col,\n  Row,\n  Spin,\n  Space,\n} from 'antd';\nimport { ProCard } from '@ant-design/pro-components';\nimport React, { useEffect, useState } from 'react';\nimport { UserService } from '@/services/user';\nimport type { UserPersonalStatsResponse, UserProfileDetailResponse } from '@/types/api';\nimport UnifiedSettingsModal from './UnifiedSettingsModal';\nimport UserInfoPopover from './UserInfoPopover';\n\n/**\n * 数据概览卡片组件\n *\n * 显示用户的个人统计数据，采用单行四列的水平布局。\n * 包括车辆、人员、预警、告警等指标的统计卡片。\n *\n * 主要功能：\n * 1. 显示车辆数量统计\n * 2. 显示人员数量统计\n * 3. 显示预警数量统计\n * 4. 显示告警数量统计\n *\n * 数据来源：\n * - 个人统计数据：通过UserService.getUserPersonalStats()获取\n *\n * 布局特点：\n * - 单行四列水平排列\n * - 每个统计项独立的卡片设计\n * - 响应式布局适配不同屏幕\n */\nconst DataOverview: React.FC = () => {\n  // 定义内联样式对象\n  const cardStyles = {\n    base: {\n      borderRadius: '8px',\n      border: '1px solid #d9d9d9',\n      height: '120px',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      backgroundColor: 'transparent', // 移除背景色\n    },\n    vehicle: {\n      // backgroundColor: '#1890ff', // 移除背景色\n      // color: 'white', // 移除白色文字\n    },\n    personnel: {\n      // backgroundColor: '#52c41a', // 移除背景色\n      // color: 'white', // 移除白色文字\n    },\n    warning: {\n      // backgroundColor: '#faad14', // 移除背景色\n      // color: 'white', // 移除白色文字\n    },\n    alert: {\n      // backgroundColor: '#f5222d', // 移除背景色\n      // color: 'white', // 移除白色文字\n    },\n  };\n  /**\n   * 个人统计数据状态管理\n   */\n  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({\n    vehicles: 0,\n    personnel: 0,\n    warnings: 0,\n    alerts: 0,\n  });\n\n  const [statsLoading, setStatsLoading] = useState(true);\n  const [statsError, setStatsError] = useState<string | null>(null);\n\n  // 获取统计数据\n  useEffect(() => {\n    const fetchStatsData = async () => {\n      try {\n        const stats = await UserService.getUserPersonalStats();\n        setPersonalStats(stats);\n        setStatsError(null);\n      } catch (error) {\n        console.error('获取统计数据失败:', error);\n        setStatsError('获取统计数据失败，请稍后重试');\n      } finally {\n        setStatsLoading(false);\n      }\n    };\n\n    fetchStatsData();\n  }, []);\n\n  return (\n    <ProCard\n      title={\n        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>\n          <BarChartOutlined style={{ fontSize: 16, color: '#1890ff' }} />\n          <span>数据概览</span>\n        </div>\n      }\n      style={{\n        marginBottom: 16,\n        borderRadius: 8,\n        border: '1px solid #d9d9d9',\n      }}\n      headStyle={{\n        borderBottom: '1px solid #f0f0f0',\n        paddingBottom: 12,\n      }}\n      bodyStyle={{\n        padding: '20px',\n      }}\n    >\n      {statsError ? (\n        <Alert\n          message=\"数据概览加载失败\"\n          description={statsError}\n          type=\"error\"\n          showIcon\n          style={{\n            borderRadius: 8,\n          }}\n        />\n      ) : (\n        <Spin spinning={statsLoading}>\n          {/* 单行四列布局 */}\n          <Row gutter={[16, 16]}>\n            {/* 车辆统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <Card\n                style={{\n                  ...cardStyles.base,\n                  ...cardStyles.vehicle,\n                }}\n                styles={{\n                  body: {\n                    padding: '20px 16px',\n                    textAlign: 'center',\n                  },\n                }}\n                hoverable\n              >\n                <div style={{ marginBottom: 12 }}>\n                  <CarOutlined\n                    style={{\n                      fontSize: 24,\n                      color: '#1890ff',\n                      marginBottom: 8,\n                    }}\n                  />\n                </div>\n                <div\n                  style={{\n                    fontSize: 32,\n                    fontWeight: 700,\n                    color: '#1890ff',\n                    lineHeight: 1,\n                    marginBottom: 8,\n                  }}\n                >\n                  {personalStats.vehicles}\n                </div>\n                <div\n                  style={{\n                    fontSize: 14,\n                    color: '#1890ff',\n                    fontWeight: 600,\n                    opacity: 0.9, // 提高透明度以确保可读性\n                  }}\n                >\n                  车辆\n                </div>\n              </Card>\n            </Col>\n\n            {/* 人员统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <Card\n                style={{\n                  ...cardStyles.base,\n                  ...cardStyles.personnel,\n                }}\n                styles={{\n                  body: {\n                    padding: '20px 16px',\n                    textAlign: 'center',\n                  },\n                }}\n                hoverable\n              >\n                <div style={{ marginBottom: 12 }}>\n                  <UsergroupAddOutlined\n                    style={{\n                      fontSize: 24,\n                      color: '#52c41a',\n                      marginBottom: 8,\n                    }}\n                  />\n                </div>\n                <div\n                  style={{\n                    fontSize: 32,\n                    fontWeight: 700,\n                    color: '#52c41a',\n                    lineHeight: 1,\n                    marginBottom: 8,\n                  }}\n                >\n                  {personalStats.personnel}\n                </div>\n                <div\n                  style={{\n                    fontSize: 14,\n                    color: '#52c41a',\n                    fontWeight: 600,\n                    opacity: 0.9, // 提高透明度以确保可读性\n                  }}\n                >\n                  人员\n                </div>\n              </Card>\n            </Col>\n\n            {/* 预警统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <Card\n                style={{\n                  ...cardStyles.base,\n                  ...cardStyles.warning,\n                }}\n                styles={{\n                  body: {\n                    padding: '20px 16px',\n                    textAlign: 'center',\n                  },\n                }}\n                hoverable\n              >\n                <div style={{ marginBottom: 12 }}>\n                  <ExclamationCircleOutlined\n                    style={{\n                      fontSize: 24,\n                      color: '#faad14',\n                      marginBottom: 8,\n                    }}\n                  />\n                </div>\n                <div\n                  style={{\n                    fontSize: 32,\n                    fontWeight: 700,\n                    color: '#faad14',\n                    lineHeight: 1,\n                    marginBottom: 8,\n                  }}\n                >\n                  {personalStats.warnings}\n                </div>\n                <div\n                  style={{\n                    fontSize: 14,\n                    color: '#faad14',\n                    fontWeight: 600,\n                    opacity: 0.9, // 提高透明度以确保可读性\n                  }}\n                >\n                  预警\n                </div>\n              </Card>\n            </Col>\n\n            {/* 告警统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <Card\n                style={{\n                  ...cardStyles.base,\n                  ...cardStyles.alert,\n                }}\n                styles={{\n                  body: {\n                    padding: '20px 16px',\n                    textAlign: 'center',\n                  },\n                }}\n                hoverable\n              >\n                <div style={{ marginBottom: 12 }}>\n                  <AlertOutlined\n                    style={{\n                      fontSize: 24,\n                      color: '#ff4d4f',\n                      marginBottom: 8,\n                    }}\n                  />\n                </div>\n                <div\n                  style={{\n                    fontSize: 32,\n                    fontWeight: 700,\n                    color: '#ff4d4f',\n                    lineHeight: 1,\n                    marginBottom: 8,\n                  }}\n                >\n                  {personalStats.alerts}\n                </div>\n                <div\n                  style={{\n                    fontSize: 14,\n                    color: '#ff4d4f',\n                    fontWeight: 600,\n                    opacity: 0.9, // 提高透明度以确保可读性\n                  }}\n                >\n                  告警\n                </div>\n              </Card>\n            </Col>\n          </Row>\n        </Spin>\n      )}\n    </ProCard>\n  );\n};\n\nexport default DataOverview;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCC8Ub;;;2BAAA;;;;;;0CAzUO;yCAQA;kDACiB;oFACmB;yCACf;;;;;;;;;;YAK5B;;;;;;;;;;;;;;;;;;;CAmBC,GACD,MAAM,eAAyB;;gBAC7B,WAAW;gBACX,MAAM,aAAa;oBACjB,MAAM;wBACJ,cAAc;wBACd,QAAQ;wBACR,QAAQ;wBACR,SAAS;wBACT,YAAY;wBACZ,gBAAgB;wBAChB,iBAAiB;oBACnB;oBACA,SAAS;oBAGT;oBACA,WAAW;oBAGX;oBACA,SAAS;oBAGT;oBACA,OAAO;oBAGP;gBACF;gBACA;;GAEC,GACD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAA4B;oBAC5E,UAAU;oBACV,WAAW;oBACX,UAAU;oBACV,QAAQ;gBACV;gBAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAC;gBACjD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAgB;gBAE5D,SAAS;gBACT,IAAA,gBAAS,EAAC;oBACR,MAAM,iBAAiB;wBACrB,IAAI;4BACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,oBAAoB;4BACpD,iBAAiB;4BACjB,cAAc;wBAChB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,aAAa;4BAC3B,cAAc;wBAChB,SAAU;4BACR,gBAAgB;wBAClB;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,qBACE,2BAAC,sBAAO;oBACN,qBACE,2BAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,YAAY;4BAAU,KAAK;wBAAE;;0CAC1D,2BAAC,uBAAgB;gCAAC,OAAO;oCAAE,UAAU;oCAAI,OAAO;gCAAU;;;;;;0CAC1D,2BAAC;0CAAK;;;;;;;;;;;;oBAGV,OAAO;wBACL,cAAc;wBACd,cAAc;wBACd,QAAQ;oBACV;oBACA,WAAW;wBACT,cAAc;wBACd,eAAe;oBACjB;oBACA,WAAW;wBACT,SAAS;oBACX;8BAEC,2BACC,2BAAC,WAAK;wBACJ,SAAQ;wBACR,aAAa;wBACb,MAAK;wBACL,QAAQ;wBACR,OAAO;4BACL,cAAc;wBAChB;;;;;6CAGF,2BAAC,UAAI;wBAAC,UAAU;kCAEd,cAAA,2BAAC,SAAG;4BAAC,QAAQ;gCAAC;gCAAI;6BAAG;;8CAEnB,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAG,IAAI;oCAAG,IAAI;oCAAG,IAAI;8CACpC,cAAA,2BAAC,UAAI;wCACH,OAAO;4CACL,GAAG,WAAW,IAAI;4CAClB,GAAG,WAAW,OAAO;wCACvB;wCACA,QAAQ;4CACN,MAAM;gDACJ,SAAS;gDACT,WAAW;4CACb;wCACF;wCACA,SAAS;;0DAET,2BAAC;gDAAI,OAAO;oDAAE,cAAc;gDAAG;0DAC7B,cAAA,2BAAC,kBAAW;oDACV,OAAO;wDACL,UAAU;wDACV,OAAO;wDACP,cAAc;oDAChB;;;;;;;;;;;0DAGJ,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,YAAY;oDACZ,OAAO;oDACP,YAAY;oDACZ,cAAc;gDAChB;0DAEC,cAAc,QAAQ;;;;;;0DAEzB,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,OAAO;oDACP,YAAY;oDACZ,SAAS;gDACX;0DACD;;;;;;;;;;;;;;;;;8CAOL,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAG,IAAI;oCAAG,IAAI;oCAAG,IAAI;8CACpC,cAAA,2BAAC,UAAI;wCACH,OAAO;4CACL,GAAG,WAAW,IAAI;4CAClB,GAAG,WAAW,SAAS;wCACzB;wCACA,QAAQ;4CACN,MAAM;gDACJ,SAAS;gDACT,WAAW;4CACb;wCACF;wCACA,SAAS;;0DAET,2BAAC;gDAAI,OAAO;oDAAE,cAAc;gDAAG;0DAC7B,cAAA,2BAAC,2BAAoB;oDACnB,OAAO;wDACL,UAAU;wDACV,OAAO;wDACP,cAAc;oDAChB;;;;;;;;;;;0DAGJ,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,YAAY;oDACZ,OAAO;oDACP,YAAY;oDACZ,cAAc;gDAChB;0DAEC,cAAc,SAAS;;;;;;0DAE1B,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,OAAO;oDACP,YAAY;oDACZ,SAAS;gDACX;0DACD;;;;;;;;;;;;;;;;;8CAOL,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAG,IAAI;oCAAG,IAAI;oCAAG,IAAI;8CACpC,cAAA,2BAAC,UAAI;wCACH,OAAO;4CACL,GAAG,WAAW,IAAI;4CAClB,GAAG,WAAW,OAAO;wCACvB;wCACA,QAAQ;4CACN,MAAM;gDACJ,SAAS;gDACT,WAAW;4CACb;wCACF;wCACA,SAAS;;0DAET,2BAAC;gDAAI,OAAO;oDAAE,cAAc;gDAAG;0DAC7B,cAAA,2BAAC,gCAAyB;oDACxB,OAAO;wDACL,UAAU;wDACV,OAAO;wDACP,cAAc;oDAChB;;;;;;;;;;;0DAGJ,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,YAAY;oDACZ,OAAO;oDACP,YAAY;oDACZ,cAAc;gDAChB;0DAEC,cAAc,QAAQ;;;;;;0DAEzB,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,OAAO;oDACP,YAAY;oDACZ,SAAS;gDACX;0DACD;;;;;;;;;;;;;;;;;8CAOL,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAG,IAAI;oCAAG,IAAI;oCAAG,IAAI;8CACpC,cAAA,2BAAC,UAAI;wCACH,OAAO;4CACL,GAAG,WAAW,IAAI;4CAClB,GAAG,WAAW,KAAK;wCACrB;wCACA,QAAQ;4CACN,MAAM;gDACJ,SAAS;gDACT,WAAW;4CACb;wCACF;wCACA,SAAS;;0DAET,2BAAC;gDAAI,OAAO;oDAAE,cAAc;gDAAG;0DAC7B,cAAA,2BAAC,oBAAa;oDACZ,OAAO;wDACL,UAAU;wDACV,OAAO;wDACP,cAAc;oDAChB;;;;;;;;;;;0DAGJ,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,YAAY;oDACZ,OAAO;oDACP,YAAY;oDACZ,cAAc;gDAChB;0DAEC,cAAc,MAAM;;;;;;0DAEvB,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,OAAO;oDACP,YAAY;oDACZ,SAAS;gDACX;0DACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUjB;eAnSM;iBAAA;gBAqSN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;ID9UD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACrjB"}