{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.1676684211925018451.hot-update.js", "src/pages/personal-center/PersonalInfo.tsx", "node_modules/@ant-design/icons/es/index.js"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='6496276995599632121';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import {\n  QuestionCircleOutlined,\n  SettingOutlined,\n} from '@ant-design/icons';\nimport {\n  Alert,\n  Space,\n  Spin,\n  Typography,\n} from 'antd';\nimport { ProCard } from '@ant-design/pro-components';\n\nimport React, { useEffect, useState } from 'react';\nimport { UserService } from '@/services/user';\nimport type { UserProfileDetailResponse } from '@/types/api';\nimport UnifiedSettingsModal from './UnifiedSettingsModal';\nimport UserInfoPopover from './UserInfoPopover';\n\nconst { Title, Text } = Typography;\n\n/**\n * 个人信息组件\n *\n * 显示用户的基本个人信息，采用简洁的卡片设计。\n * 用户名支持悬浮显示详细信息（电话、邮箱、注册时间等）。\n *\n * 主要功能：\n * 1. 显示用户头像（使用姓名首字母）\n * 2. 显示用户姓名（支持气泡卡片显示详细信息）\n * 3. 显示最后登录时间和登录团队\n * 4. 提供设置入口\n *\n * 数据来源：\n * - 用户详细信息：通过UserService.getUserProfileDetail()获取\n */\nconst PersonalInfo: React.FC = () => {\n  /**\n   * 用户详细信息状态管理\n   */\n  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({\n    name: '',\n    position: '',\n    email: '',\n    telephone: '',\n    registerDate: '',\n    lastLoginTime: '',\n    lastLoginTeam: '',\n    teamCount: 0,\n    avatar: '',\n  });\n\n  const [userInfoLoading, setUserInfoLoading] = useState(true);\n  const [userInfoError, setUserInfoError] = useState<string | null>(null);\n\n  // Modal状态管理\n  const [settingsModalVisible, setSettingsModalVisible] = useState(false);\n\n  // 获取用户数据\n  useEffect(() => {\n    const fetchUserData = async () => {\n      try {\n        const userDetail = await UserService.getUserProfileDetail();\n        setUserInfo(userDetail);\n        setUserInfoError(null);\n      } catch (error) {\n        console.error('获取用户详细信息失败:', error);\n        setUserInfoError('获取用户详细信息失败，请稍后重试');\n      } finally {\n        setUserInfoLoading(false);\n      }\n    };\n\n    fetchUserData();\n  }, []);\n\n  return (\n    <>\n      <ProCard\n        style={{\n          marginBottom: 16,\n          borderRadius: 8,\n          border: '1px solid #d9d9d9',\n          position: 'relative',\n        }}\n        bodyStyle={{\n          padding: '20px',\n        }}\n      >\n        {/* 右上角图标区域 */}\n        <div style={{\n          position: 'absolute',\n          top: '20px',\n          right: '20px',\n          zIndex: 10,\n        }}>\n          <Space size={16}>\n            <UserInfoPopover userInfo={userInfo}>\n              <QuestionCircleOutlined\n                style={{\n                  fontSize: 18,\n                  color: '#8c8c8c',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease',\n                  padding: '4px',\n                  borderRadius: '50%',\n                  background: 'transparent',\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.color = '#1890ff';\n                  e.currentTarget.style.background = 'rgba(24, 144, 255, 0.08)';\n                  e.currentTarget.style.transform = 'scale(1.1)';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.color = '#8c8c8c';\n                  e.currentTarget.style.background = 'transparent';\n                  e.currentTarget.style.transform = 'scale(1)';\n                }}\n              />\n            </UserInfoPopover>\n            <SettingOutlined\n              style={{\n                fontSize: 18,\n                color: '#8c8c8c',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease',\n                padding: '4px',\n                borderRadius: '50%',\n                background: 'transparent',\n              }}\n              onMouseEnter={(e) => {\n                e.currentTarget.style.color = '#1890ff';\n                e.currentTarget.style.background = 'rgba(24, 144, 255, 0.08)';\n                e.currentTarget.style.transform = 'scale(1.1)';\n              }}\n              onMouseLeave={(e) => {\n                e.currentTarget.style.color = '#8c8c8c';\n                e.currentTarget.style.background = 'transparent';\n                e.currentTarget.style.transform = 'scale(1)';\n              }}\n              onClick={() => setSettingsModalVisible(true)}\n            />\n          </Space>\n        </div>\n\n        {userInfoError ? (\n          <Alert\n            message=\"个人信息加载失败\"\n            description={userInfoError}\n            type=\"error\"\n            showIcon\n            style={{\n              borderRadius: 12,\n              border: 'none',\n            }}\n          />\n        ) : (\n          <Spin spinning={userInfoLoading}>\n            {/* 问候区域 */}\n            <div style={{\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              padding: '16px 0',\n              marginTop: '20px', // 为右上角图标留出空间\n            }}>\n              {/* 问候语 */}\n              <Typography.Title\n                level={3}\n                style={{\n                  margin: 0,\n                  fontSize: 20,\n                  color: '#262626',\n                }}\n              >\n                您好，{userInfo.name || '加载中...'}\n              </Typography.Title>\n            </div>\n          </Spin>\n        )}\n      </ProCard>\n\n      {/* 统一设置Modal */}\n      <UnifiedSettingsModal\n        visible={settingsModalVisible}\n        onCancel={() => setSettingsModalVisible(false)}\n        userInfo={userInfo}\n        onSuccess={() => {\n          // 可以在这里刷新用户信息\n          console.log('设置操作成功');\n        }}\n      />\n    </>\n  );\n};\n\nexport default PersonalInfo;\n", "import Context from \"./components/Context\";\nexport * from \"./icons\";\nexport * from \"./components/twoTonePrimaryColor\";\nexport { default as createFromIconfontCN } from \"./components/IconFont\";\nexport { default } from \"./components/Icon\";\nexport var IconProvider = Context.Provider;"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCCgMb;;;2BAAA;;;;;;;0CAhMO;yCAMA;kDACiB;sEAEmB;yCACf;oFAEK;+EACL;;;;;;;;;;YAE5B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAiBlC,MAAM,eAAyB;;gBAI7B,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAA4B;oBAClE,MAAM;oBACN,UAAU;oBACV,OAAO;oBACP,WAAW;oBACX,cAAc;oBACd,eAAe;oBACf,eAAe;oBACf,WAAW;oBACX,QAAQ;gBACV;gBAEA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAC;gBACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;gBAGlE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,IAAA,eAAQ,EAAC;gBAGjE,IAAA,gBAAS,EAAC;oBACR,MAAM,gBAAgB;wBACpB,IAAI;4BACF,MAAM,aAAa,MAAM,iBAAW,CAAC,oBAAoB;4BACzD,YAAY;4BACZ,iBAAiB;wBACnB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,eAAe;4BAC7B,iBAAiB;wBACnB,SAAU;4BACR,mBAAmB;wBACrB;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,OACE;;wBACE,2BAAC,sBAAO;4BACN,OAAO;gCACL,cAAc;gCACd,cAAc;gCACd,QAAQ;gCACR,UAAU;4BACZ;4BACA,WAAW;gCACT,SAAS;4BACX;;gCAGA,2BAAC;oCAAI,OAAO;wCACV,UAAU;wCACV,KAAK;wCACL,OAAO;wCACP,QAAQ;oCACV;8CACE,2BAAC,WAAK;wCAAC,MAAM;;4CACX,2BAAC,wBAAe;gDAAC,UAAU;0DACzB,2BAAC,6BAAsB;oDACrB,OAAO;wDACL,UAAU;wDACV,OAAO;wDACP,QAAQ;wDACR,YAAY;wDACZ,SAAS;wDACT,cAAc;wDACd,YAAY;oDACd;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;wDAC9B,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;wDACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oDACpC;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;wDAC9B,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;wDACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oDACpC;;;;;;;;;;;4CAGJ,2BAAC,sBAAe;gDACd,OAAO;oDACL,UAAU;oDACV,OAAO;oDACP,QAAQ;oDACR,YAAY;oDACZ,SAAS;oDACT,cAAc;oDACd,YAAY;gDACd;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;oDAC9B,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;oDACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;gDACpC;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;oDAC9B,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;oDACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;gDACpC;gDACA,SAAS,IAAM,wBAAwB;;;;;;;;;;;;;;;;;gCAK5C,gBACC,2BAAC,WAAK;oCACJ,SAAQ;oCACR,aAAa;oCACb,MAAK;oCACL,QAAQ;oCACR,OAAO;wCACL,cAAc;wCACd,QAAQ;oCACV;;;;;2CAGF,2BAAC,UAAI;oCAAC,UAAU;8CAEd,2BAAC;wCAAI,OAAO;4CACV,SAAS;4CACT,gBAAgB;4CAChB,YAAY;4CACZ,SAAS;4CACT,WAAW;wCACb;kDAEE,2BAAC,gBAAU,CAAC,KAAK;4CACf,OAAO;4CACP,OAAO;gDACL,QAAQ;gDACR,UAAU;gDACV,OAAO;4CACT;;gDACD;gDACK,SAAS,IAAI,IAAI;;;;;;;;;;;;;;;;;;;;;;;wBAQ/B,2BAAC,6BAAoB;4BACnB,SAAS;4BACT,UAAU,IAAM,wBAAwB;4BACxC,UAAU;4BACV,WAAW;gCAET,QAAQ,GAAG,CAAC;4BACd;;;;;;;;YAIR;eA9JM;iBAAA;gBAgKN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC9LJ,YAAY;2BAAZ;;gBAFS,oBAAoB;2BAApB,iBAAoB;;gBAC/B,OAAO;2BAAP,aAAO;;;;;uEAJI;4CACN;4CACA;wEACkC;oEACxB;YACjB,IAAI,eAAe,gBAAO,CAAC,QAAQ;;IFF5B;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACrjB"}