globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/pages/personal-center/DataOverview.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _user = __mako_require__("src/services/user.ts");
            var _UnifiedSettingsModal = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/UnifiedSettingsModal.tsx"));
            var _UserInfoPopover = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/UserInfoPopover.tsx"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const DataOverview = ({ userInfo })=>{
                _s();
                // 定义内联样式对象
                const cardStyles = {
                    base: {
                        borderRadius: '8px',
                        border: '1px solid #d9d9d9',
                        height: '120px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: 'transparent'
                    },
                    vehicle: {
                    },
                    personnel: {
                    },
                    warning: {
                    },
                    alert: {
                    }
                };
                /**
   * 个人统计数据状态管理
   */ const [personalStats, setPersonalStats] = (0, _react.useState)({
                    vehicles: 0,
                    personnel: 0,
                    warnings: 0,
                    alerts: 0
                });
                const [statsLoading, setStatsLoading] = (0, _react.useState)(true);
                const [statsError, setStatsError] = (0, _react.useState)(null);
                // Modal状态管理
                const [settingsModalVisible, setSettingsModalVisible] = (0, _react.useState)(false);
                // 获取统计数据
                (0, _react.useEffect)(()=>{
                    const fetchStatsData = async ()=>{
                        try {
                            const stats = await _user.UserService.getUserPersonalStats();
                            setPersonalStats(stats);
                            setStatsError(null);
                        } catch (error) {
                            console.error('获取统计数据失败:', error);
                            setStatsError('获取统计数据失败，请稍后重试');
                        } finally{
                            setStatsLoading(false);
                        }
                    };
                    fetchStatsData();
                }, []);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: 8
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.BarChartOutlined, {
                                        style: {
                                            fontSize: 16,
                                            color: '#1890ff'
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 118,
                                        columnNumber: 13
                                    }, void 0),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                        children: "数据概览"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 119,
                                        columnNumber: 13
                                    }, void 0)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                lineNumber: 117,
                                columnNumber: 11
                            }, void 0),
                            extra: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                size: 16,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_UserInfoPopover.default, {
                                        userInfo: userInfo,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.QuestionCircleOutlined, {
                                            style: {
                                                fontSize: 18,
                                                color: '#8c8c8c',
                                                cursor: 'pointer',
                                                transition: 'all 0.2s ease',
                                                padding: '4px',
                                                borderRadius: '50%',
                                                background: 'transparent'
                                            },
                                            onMouseEnter: (e)=>{
                                                e.currentTarget.style.color = '#1890ff';
                                                e.currentTarget.style.background = 'rgba(24, 144, 255, 0.08)';
                                                e.currentTarget.style.transform = 'scale(1.1)';
                                            },
                                            onMouseLeave: (e)=>{
                                                e.currentTarget.style.color = '#8c8c8c';
                                                e.currentTarget.style.background = 'transparent';
                                                e.currentTarget.style.transform = 'scale(1)';
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/DataOverview.tsx",
                                            lineNumber: 125,
                                            columnNumber: 15
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 124,
                                        columnNumber: 13
                                    }, void 0),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {
                                        style: {
                                            fontSize: 18,
                                            color: '#8c8c8c',
                                            cursor: 'pointer',
                                            transition: 'all 0.2s ease',
                                            padding: '4px',
                                            borderRadius: '50%',
                                            background: 'transparent'
                                        },
                                        onMouseEnter: (e)=>{
                                            e.currentTarget.style.color = '#1890ff';
                                            e.currentTarget.style.background = 'rgba(24, 144, 255, 0.08)';
                                            e.currentTarget.style.transform = 'scale(1.1)';
                                        },
                                        onMouseLeave: (e)=>{
                                            e.currentTarget.style.color = '#8c8c8c';
                                            e.currentTarget.style.background = 'transparent';
                                            e.currentTarget.style.transform = 'scale(1)';
                                        },
                                        onClick: ()=>setSettingsModalVisible(true)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                        lineNumber: 147,
                                        columnNumber: 13
                                    }, void 0)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                lineNumber: 123,
                                columnNumber: 11
                            }, void 0),
                            style: {
                                marginBottom: 16,
                                borderRadius: 8,
                                border: '1px solid #d9d9d9'
                            },
                            headStyle: {
                                borderBottom: '1px solid #f0f0f0',
                                paddingBottom: 12
                            },
                            bodyStyle: {
                                padding: '20px'
                            },
                            children: statsError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                                message: "数据概览加载失败",
                                description: statsError,
                                type: "error",
                                showIcon: true,
                                style: {
                                    borderRadius: 8
                                }
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                lineNumber: 185,
                                columnNumber: 9
                            }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                                spinning: statsLoading,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                    gutter: [
                                        16,
                                        16
                                    ],
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            xs: 12,
                                            sm: 6,
                                            md: 6,
                                            lg: 6,
                                            xl: 6,
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                style: {
                                                    ...cardStyles.base,
                                                    ...cardStyles.vehicle
                                                },
                                                styles: {
                                                    body: {
                                                        padding: '20px 16px',
                                                        textAlign: 'center'
                                                    }
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            marginBottom: 12
                                                        },
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CarOutlined, {
                                                            style: {
                                                                fontSize: 24,
                                                                color: '#1890ff',
                                                                marginBottom: 8
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/DataOverview.tsx",
                                                            lineNumber: 213,
                                                            columnNumber: 19
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 212,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            fontSize: 32,
                                                            fontWeight: 700,
                                                            color: '#1890ff',
                                                            lineHeight: 1,
                                                            marginBottom: 8
                                                        },
                                                        children: personalStats.vehicles
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 221,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            fontSize: 14,
                                                            color: '#1890ff',
                                                            fontWeight: 600,
                                                            opacity: 0.9
                                                        },
                                                        children: "车辆"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 232,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 200,
                                                columnNumber: 15
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/DataOverview.tsx",
                                            lineNumber: 199,
                                            columnNumber: 13
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            xs: 12,
                                            sm: 6,
                                            md: 6,
                                            lg: 6,
                                            xl: 6,
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                style: {
                                                    ...cardStyles.base,
                                                    ...cardStyles.personnel
                                                },
                                                styles: {
                                                    body: {
                                                        padding: '20px 16px',
                                                        textAlign: 'center'
                                                    }
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            marginBottom: 12
                                                        },
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UsergroupAddOutlined, {
                                                            style: {
                                                                fontSize: 24,
                                                                color: '#52c41a',
                                                                marginBottom: 8
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/DataOverview.tsx",
                                                            lineNumber: 260,
                                                            columnNumber: 19
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 259,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            fontSize: 32,
                                                            fontWeight: 700,
                                                            color: '#52c41a',
                                                            lineHeight: 1,
                                                            marginBottom: 8
                                                        },
                                                        children: personalStats.personnel
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 268,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            fontSize: 14,
                                                            color: '#52c41a',
                                                            fontWeight: 600,
                                                            opacity: 0.9
                                                        },
                                                        children: "人员"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 279,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 247,
                                                columnNumber: 15
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/DataOverview.tsx",
                                            lineNumber: 246,
                                            columnNumber: 13
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            xs: 12,
                                            sm: 6,
                                            md: 6,
                                            lg: 6,
                                            xl: 6,
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                style: {
                                                    ...cardStyles.base,
                                                    ...cardStyles.warning
                                                },
                                                styles: {
                                                    body: {
                                                        padding: '20px 16px',
                                                        textAlign: 'center'
                                                    }
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            marginBottom: 12
                                                        },
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                                            style: {
                                                                fontSize: 24,
                                                                color: '#faad14',
                                                                marginBottom: 8
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/DataOverview.tsx",
                                                            lineNumber: 307,
                                                            columnNumber: 19
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 306,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            fontSize: 32,
                                                            fontWeight: 700,
                                                            color: '#faad14',
                                                            lineHeight: 1,
                                                            marginBottom: 8
                                                        },
                                                        children: personalStats.warnings
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 315,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            fontSize: 14,
                                                            color: '#faad14',
                                                            fontWeight: 600,
                                                            opacity: 0.9
                                                        },
                                                        children: "预警"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 326,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 294,
                                                columnNumber: 15
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/DataOverview.tsx",
                                            lineNumber: 293,
                                            columnNumber: 13
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            xs: 12,
                                            sm: 6,
                                            md: 6,
                                            lg: 6,
                                            xl: 6,
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                style: {
                                                    ...cardStyles.base,
                                                    ...cardStyles.alert
                                                },
                                                styles: {
                                                    body: {
                                                        padding: '20px 16px',
                                                        textAlign: 'center'
                                                    }
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            marginBottom: 12
                                                        },
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.AlertOutlined, {
                                                            style: {
                                                                fontSize: 24,
                                                                color: '#ff4d4f',
                                                                marginBottom: 8
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/DataOverview.tsx",
                                                            lineNumber: 354,
                                                            columnNumber: 19
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 353,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            fontSize: 32,
                                                            fontWeight: 700,
                                                            color: '#ff4d4f',
                                                            lineHeight: 1,
                                                            marginBottom: 8
                                                        },
                                                        children: personalStats.alerts
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 362,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            fontSize: 14,
                                                            color: '#ff4d4f',
                                                            fontWeight: 600,
                                                            opacity: 0.9
                                                        },
                                                        children: "告警"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/DataOverview.tsx",
                                                        lineNumber: 373,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                                lineNumber: 341,
                                                columnNumber: 15
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/DataOverview.tsx",
                                            lineNumber: 340,
                                            columnNumber: 13
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 197,
                                    columnNumber: 11
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                lineNumber: 195,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/DataOverview.tsx",
                            lineNumber: 115,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_UnifiedSettingsModal.default, {
                            visible: settingsModalVisible,
                            onCancel: ()=>setSettingsModalVisible(false),
                            userInfo: userInfo,
                            onSuccess: ()=>{
                                // 可以在这里刷新用户信息
                                console.log('设置操作成功');
                            }
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/DataOverview.tsx",
                            lineNumber: 391,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true);
            };
            _s(DataOverview, "mVz1z7TMVznJTJuxXfd+zFH2fZo=");
            _c = DataOverview;
            var _default = DataOverview;
            var _c;
            $RefreshReg$(_c, "DataOverview");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/pages/personal-center/UserInfoPopover.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/react/index.js"));
            var _UserInfoPopovermodulecssasmodule = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/UserInfoPopover.module.css?asmodule"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            const { Text } = _antd.Typography;
            /**
 * 用户信息气泡卡片组件
 *
 * 在用户名上显示详细的用户信息，包括电话、邮箱、注册时间等。
 * 采用Popover组件实现悬浮显示效果。
 *
 * 主要功能：
 * 1. 显示用户邮箱
 * 2. 显示用户电话
 * 3. 显示注册时间
 * 4. 显示最后登录时间
 * 5. 显示最后登录团队
 *
 * 使用方式：
 * <UserInfoPopover userInfo={userInfo}>
 *   <span>用户名</span>
 * </UserInfoPopover>
 */ const UserInfoPopover = ({ userInfo, children })=>{
                const popoverContent = /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    className: _UserInfoPopovermodulecssasmodule.default.popoverContent,
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        direction: "vertical",
                        size: 12,
                        style: {
                            width: '100%'
                        },
                        children: [
                            userInfo.email && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                className: `${_UserInfoPopovermodulecssasmodule.default.infoItem} ${_UserInfoPopovermodulecssasmodule.default.email}`,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        className: _UserInfoPopovermodulecssasmodule.default.iconWrapper,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {
                                            className: _UserInfoPopovermodulecssasmodule.default.icon,
                                            style: {
                                                color: '#1890ff'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                            lineNumber: 54,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                        lineNumber: 53,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        className: _UserInfoPopovermodulecssasmodule.default.infoContent,
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                className: _UserInfoPopovermodulecssasmodule.default.label,
                                                children: "邮箱"
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                                lineNumber: 57,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                className: _UserInfoPopovermodulecssasmodule.default.value,
                                                copyable: true,
                                                children: userInfo.email
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                                lineNumber: 60,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                        lineNumber: 56,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                lineNumber: 52,
                                columnNumber: 11
                            }, this),
                            userInfo.telephone && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                className: `${_UserInfoPopovermodulecssasmodule.default.infoItem} ${_UserInfoPopovermodulecssasmodule.default.phone}`,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        className: _UserInfoPopovermodulecssasmodule.default.iconWrapper,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PhoneOutlined, {
                                            className: _UserInfoPopovermodulecssasmodule.default.icon,
                                            style: {
                                                color: '#52c41a'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                            lineNumber: 70,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                        lineNumber: 69,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        className: _UserInfoPopovermodulecssasmodule.default.infoContent,
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                className: _UserInfoPopovermodulecssasmodule.default.label,
                                                children: "电话"
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                                lineNumber: 73,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                className: _UserInfoPopovermodulecssasmodule.default.value,
                                                copyable: true,
                                                children: userInfo.telephone
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                                lineNumber: 76,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                        lineNumber: 72,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                lineNumber: 68,
                                columnNumber: 11
                            }, this),
                            (userInfo.email || userInfo.telephone) && userInfo.registerDate && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                                className: _UserInfoPopovermodulecssasmodule.default.divider
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                lineNumber: 84,
                                columnNumber: 11
                            }, this),
                            userInfo.registerDate && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                className: `${_UserInfoPopovermodulecssasmodule.default.infoItem} ${_UserInfoPopovermodulecssasmodule.default.register}`,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        className: _UserInfoPopovermodulecssasmodule.default.iconWrapper,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {
                                            className: _UserInfoPopovermodulecssasmodule.default.icon,
                                            style: {
                                                color: '#722ed1'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                            lineNumber: 91,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                        lineNumber: 90,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        className: _UserInfoPopovermodulecssasmodule.default.infoContent,
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                className: _UserInfoPopovermodulecssasmodule.default.label,
                                                children: "注册时间"
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                                lineNumber: 94,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                className: _UserInfoPopovermodulecssasmodule.default.value,
                                                children: userInfo.registerDate
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                                lineNumber: 97,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                        lineNumber: 93,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                lineNumber: 89,
                                columnNumber: 11
                            }, this),
                            userInfo.lastLoginTime && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                className: `${_UserInfoPopovermodulecssasmodule.default.infoItem} ${_UserInfoPopovermodulecssasmodule.default.lastLogin}`,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        className: _UserInfoPopovermodulecssasmodule.default.iconWrapper,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {
                                            className: _UserInfoPopovermodulecssasmodule.default.icon,
                                            style: {
                                                color: '#fa8c16'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                            lineNumber: 107,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                        lineNumber: 106,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        className: _UserInfoPopovermodulecssasmodule.default.infoContent,
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                className: _UserInfoPopovermodulecssasmodule.default.label,
                                                children: "最后登录"
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                                lineNumber: 110,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                className: _UserInfoPopovermodulecssasmodule.default.value,
                                                children: userInfo.lastLoginTime
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                                lineNumber: 113,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                        lineNumber: 109,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                lineNumber: 105,
                                columnNumber: 11
                            }, this),
                            userInfo.lastLoginTeam && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                className: `${_UserInfoPopovermodulecssasmodule.default.infoItem} ${_UserInfoPopovermodulecssasmodule.default.team}`,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        className: _UserInfoPopovermodulecssasmodule.default.iconWrapper,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                            className: _UserInfoPopovermodulecssasmodule.default.icon,
                                            style: {
                                                color: '#13c2c2'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                            lineNumber: 123,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                        lineNumber: 122,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        className: _UserInfoPopovermodulecssasmodule.default.infoContent,
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                className: _UserInfoPopovermodulecssasmodule.default.label,
                                                children: "登录团队"
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                                lineNumber: 126,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                className: _UserInfoPopovermodulecssasmodule.default.value,
                                                children: userInfo.lastLoginTeam
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                                lineNumber: 129,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                        lineNumber: 125,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                                lineNumber: 121,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                        lineNumber: 49,
                        columnNumber: 7
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                    lineNumber: 48,
                    columnNumber: 5
                }, this);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popover, {
                    content: popoverContent,
                    title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        className: _UserInfoPopovermodulecssasmodule.default.popoverTitle,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            strong: true,
                            children: "用户详细信息"
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                            lineNumber: 144,
                            columnNumber: 11
                        }, void 0)
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                        lineNumber: 143,
                        columnNumber: 9
                    }, void 0),
                    trigger: [
                        "hover",
                        "click"
                    ],
                    placement: "bottomLeft",
                    styles: {
                        body: {
                            padding: '16px 20px',
                            borderRadius: '12px',
                            background: '#ffffff',
                            maxWidth: '380px',
                            boxShadow: '0 12px 32px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08)',
                            border: '1px solid rgba(0, 0, 0, 0.06)'
                        }
                    },
                    arrow: {
                        pointAtCenter: true
                    },
                    mouseEnterDelay: 0.3,
                    mouseLeaveDelay: 0.1,
                    fresh: false,
                    zIndex: 1060,
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                        className: _UserInfoPopovermodulecssasmodule.default.trigger,
                        children: children
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                        lineNumber: 167,
                        columnNumber: 7
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/UserInfoPopover.tsx",
                    lineNumber: 140,
                    columnNumber: 5
                }, this);
            };
            _c = UserInfoPopover;
            var _default = UserInfoPopover;
            var _c;
            $RefreshReg$(_c, "UserInfoPopover");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/pages/personal-center/UnifiedSettingsModal.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            /**
 * 统一设置Modal组件
 *
 * 提供个人信息修改和新建团队功能的统一界面。
 * 使用Tab页面结构组织不同的设置功能。
 *
 * 主要功能：
 * 1. 个人信息修改Tab：编辑用户基本信息
 * 2. 新建团队Tab：创建新团队
 *
 * Props:
 * - visible: 控制Modal显示/隐藏
 * - onCancel: 取消操作回调
 * - onSuccess: 操作成功回调
 * - userInfo: 当前用户信息
 */ const UnifiedSettingsModal = ({ visible, onCancel, onSuccess, userInfo })=>{
                _s();
                const [personalForm] = _antd.Form.useForm();
                const [teamForm] = _antd.Form.useForm();
                const [activeTab, setActiveTab] = (0, _react.useState)('personal');
                // 当Modal打开时，填充个人信息表单数据
                (0, _react.useEffect)(()=>{
                    if (visible && userInfo) personalForm.setFieldsValue({
                        name: userInfo.name,
                        email: userInfo.email,
                        telephone: userInfo.telephone,
                        position: userInfo.position
                    });
                }, [
                    visible,
                    userInfo,
                    personalForm
                ]);
                // 处理个人信息提交
                const handlePersonalSubmit = async ()=>{
                    try {
                        const values = await personalForm.validateFields();
                        console.log('更新个人信息:', values);
                        // TODO: 调用更新用户信息的API
                        // await UserService.updateUserProfile(values);
                        _antd.message.success('个人信息更新成功！');
                        onSuccess === null || onSuccess === void 0 || onSuccess();
                        onCancel();
                    } catch (error) {
                        console.error('更新个人信息失败:', error);
                        _antd.message.error('更新个人信息失败，请稍后重试');
                    }
                };
                // 处理团队创建提交
                const handleTeamSubmit = async ()=>{
                    try {
                        const values = await teamForm.validateFields();
                        console.log('创建团队:', values);
                        // TODO: 调用创建团队的API
                        // await TeamService.createTeam(values);
                        _antd.message.success('团队创建成功！');
                        teamForm.resetFields();
                        onSuccess === null || onSuccess === void 0 || onSuccess();
                        onCancel();
                    } catch (error) {
                        console.error('创建团队失败:', error);
                        _antd.message.error('创建团队失败，请稍后重试');
                    }
                };
                // 处理取消操作
                const handleCancel = ()=>{
                    personalForm.resetFields();
                    teamForm.resetFields();
                    setActiveTab('personal');
                    onCancel();
                };
                // 根据当前Tab决定提交操作
                const handleOk = ()=>{
                    if (activeTab === 'personal') handlePersonalSubmit();
                    else handleTeamSubmit();
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                    title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        align: "center",
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {
                                style: {
                                    fontSize: 18,
                                    color: '#1890ff'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                lineNumber: 120,
                                columnNumber: 11
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                level: 4,
                                style: {
                                    margin: 0,
                                    fontSize: 16
                                },
                                children: "设置"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                lineNumber: 121,
                                columnNumber: 11
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                        lineNumber: 119,
                        columnNumber: 9
                    }, void 0),
                    open: visible,
                    onOk: handleOk,
                    onCancel: handleCancel,
                    okText: activeTab === 'personal' ? '保存设置' : '创建团队',
                    cancelText: "取消",
                    destroyOnClose: true,
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                        activeKey: activeTab,
                        onChange: setActiveTab,
                        style: {
                            marginTop: -8
                        },
                        items: [
                            {
                                key: 'personal',
                                label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                    align: "center",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                            fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                            lineNumber: 144,
                                            columnNumber: 17
                                        }, void 0),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                            children: "个人信息"
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                            lineNumber: 145,
                                            columnNumber: 17
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                    lineNumber: 143,
                                    columnNumber: 15
                                }, void 0),
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                marginBottom: 16
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                style: {
                                                    color: '#8c8c8c',
                                                    fontSize: 14
                                                },
                                                children: "编辑您的个人信息和偏好设置"
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                                lineNumber: 151,
                                                columnNumber: 19
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                            lineNumber: 150,
                                            columnNumber: 17
                                        }, void 0),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                            form: personalForm,
                                            layout: "vertical",
                                            requiredMark: false,
                                            autoComplete: "off",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontWeight: 600,
                                                            fontSize: 14
                                                        },
                                                        children: "姓名"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                                        lineNumber: 165,
                                                        columnNumber: 23
                                                    }, void 0),
                                                    name: "name",
                                                    rules: [
                                                        {
                                                            required: true,
                                                            message: '请输入姓名'
                                                        },
                                                        {
                                                            min: 2,
                                                            message: '姓名至少2个字符'
                                                        },
                                                        {
                                                            max: 20,
                                                            message: '姓名不能超过20个字符'
                                                        }
                                                    ],
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                        placeholder: "请输入姓名",
                                                        style: {
                                                            borderRadius: 6,
                                                            fontSize: 14
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                                        lineNumber: 176,
                                                        columnNumber: 21
                                                    }, void 0)
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                                    lineNumber: 163,
                                                    columnNumber: 19
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontWeight: 600,
                                                            fontSize: 14
                                                        },
                                                        children: "职位"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                                        lineNumber: 188,
                                                        columnNumber: 23
                                                    }, void 0),
                                                    name: "position",
                                                    rules: [
                                                        {
                                                            max: 50,
                                                            message: '职位不能超过50个字符'
                                                        }
                                                    ],
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                        placeholder: "请输入职位（可选）",
                                                        style: {
                                                            borderRadius: 6,
                                                            fontSize: 14
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                                        lineNumber: 197,
                                                        columnNumber: 21
                                                    }, void 0)
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                                    lineNumber: 186,
                                                    columnNumber: 19
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontWeight: 600,
                                                            fontSize: 14
                                                        },
                                                        children: "邮箱"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                                        lineNumber: 209,
                                                        columnNumber: 23
                                                    }, void 0),
                                                    name: "email",
                                                    rules: [
                                                        {
                                                            required: true,
                                                            message: '请输入邮箱'
                                                        },
                                                        {
                                                            type: 'email',
                                                            message: '请输入有效的邮箱地址'
                                                        }
                                                    ],
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                        placeholder: "请输入邮箱",
                                                        style: {
                                                            borderRadius: 6,
                                                            fontSize: 14
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                                        lineNumber: 219,
                                                        columnNumber: 21
                                                    }, void 0)
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                                    lineNumber: 207,
                                                    columnNumber: 19
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontWeight: 600,
                                                            fontSize: 14
                                                        },
                                                        children: "电话"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                                        lineNumber: 231,
                                                        columnNumber: 23
                                                    }, void 0),
                                                    name: "telephone",
                                                    rules: [
                                                        {
                                                            pattern: /^1[3-9]\d{9}$/,
                                                            message: '请输入有效的手机号码'
                                                        }
                                                    ],
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                        placeholder: "请输入电话（可选）",
                                                        style: {
                                                            borderRadius: 6,
                                                            fontSize: 14
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                                        lineNumber: 240,
                                                        columnNumber: 21
                                                    }, void 0)
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                                    lineNumber: 229,
                                                    columnNumber: 19
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                            lineNumber: 156,
                                            columnNumber: 17
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                    lineNumber: 149,
                                    columnNumber: 15
                                }, void 0)
                            },
                            {
                                key: 'team',
                                label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                    align: "center",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                            fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                            lineNumber: 256,
                                            columnNumber: 17
                                        }, void 0),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                            children: "新建团队"
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                            lineNumber: 257,
                                            columnNumber: 17
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                    lineNumber: 255,
                                    columnNumber: 15
                                }, void 0),
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                marginBottom: 24,
                                                textAlign: 'center'
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                style: {
                                                    color: '#8c8c8c',
                                                    fontSize: 14,
                                                    lineHeight: 1.6
                                                },
                                                children: "创建一个新的团队来协作管理项目和任务"
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                                lineNumber: 263,
                                                columnNumber: 19
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                            lineNumber: 262,
                                            columnNumber: 17
                                        }, void 0),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                            form: teamForm,
                                            layout: "vertical",
                                            requiredMark: false,
                                            autoComplete: "off",
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                                label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    style: {
                                                        fontWeight: 600,
                                                        fontSize: 15
                                                    },
                                                    children: "团队名称"
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                                    lineNumber: 277,
                                                    columnNumber: 23
                                                }, void 0),
                                                name: "teamName",
                                                rules: [
                                                    {
                                                        required: true,
                                                        message: '请输入团队名称'
                                                    },
                                                    {
                                                        min: 2,
                                                        message: '团队名称至少2个字符'
                                                    },
                                                    {
                                                        max: 50,
                                                        message: '团队名称不能超过50个字符'
                                                    }
                                                ],
                                                style: {
                                                    marginBottom: 0
                                                },
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                    placeholder: "请输入团队名称",
                                                    size: "large",
                                                    style: {
                                                        borderRadius: 8,
                                                        fontSize: 15,
                                                        padding: '12px 16px',
                                                        border: '2px solid #d9d9d9',
                                                        transition: 'all 0.3s ease'
                                                    },
                                                    onFocus: (e)=>{
                                                        e.target.style.borderColor = '#1890ff';
                                                        e.target.style.boxShadow = '0 0 0 2px rgba(24, 144, 255, 0.1)';
                                                    },
                                                    onBlur: (e)=>{
                                                        e.target.style.borderColor = '#d9d9d9';
                                                        e.target.style.boxShadow = 'none';
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                                    lineNumber: 289,
                                                    columnNumber: 21
                                                }, void 0)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                                lineNumber: 275,
                                                columnNumber: 19
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                            lineNumber: 268,
                                            columnNumber: 17
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                    lineNumber: 261,
                                    columnNumber: 15
                                }, void 0)
                            }
                        ]
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                        lineNumber: 135,
                        columnNumber: 7
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                    lineNumber: 117,
                    columnNumber: 5
                }, this);
            };
            _s(UnifiedSettingsModal, "DDUCm/Qsce57NmLc3imUABC/62A=", false, function() {
                return [
                    _antd.Form.useForm,
                    _antd.Form.useForm
                ];
            });
            _c = UnifiedSettingsModal;
            var _default = UnifiedSettingsModal;
            var _c;
            $RefreshReg$(_c, "UnifiedSettingsModal");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '8717034121622362365';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/settings/index.tsx": [
            "p__settings__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.15130806917278659141.hot-update.js.map