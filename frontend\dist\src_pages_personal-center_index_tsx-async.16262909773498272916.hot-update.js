globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/pages/personal-center/DataOverview.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _user = __mako_require__("src/services/user.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            /**
 * 数据概览卡片组件
 *
 * 使用 Ant Design Pro Components 的 StatisticCard 组件显示用户的个人统计数据，
 * 采用单行四列的响应式网格布局。包括车辆、人员、预警、告警等指标的统计卡片。
 *
 * 主要功能：
 * 1. 显示车辆数量统计 - 使用车辆图标，蓝色主题
 * 2. 显示人员数量统计 - 使用用户组图标，绿色主题
 * 3. 显示预警数量统计 - 使用感叹号图标，橙色主题
 * 4. 显示告警数量统计 - 使用警告图标，红色主题
 *
 * 数据来源：
 * - 个人统计数据：通过UserService.getUserPersonalStats()获取
 *
 * 布局特点：
 * - 使用 StatisticCard 组件提供专业的数据展示
 * - 单行四列水平排列，响应式布局适配不同屏幕
 * - 每个统计项都有语义化的图标和颜色主题
 * - 统一的卡片样式和高度
 */ const DataOverview = ()=>{
                _s();
                /**
   * 响应式检测
   */ const { useBreakpoint } = _antd.Grid;
                const screens = useBreakpoint();
                /**
   * 个人统计数据状态管理
   */ const [personalStats, setPersonalStats] = (0, _react.useState)({
                    vehicles: 0,
                    personnel: 0,
                    warnings: 0,
                    alerts: 0
                });
                const [statsLoading, setStatsLoading] = (0, _react.useState)(true);
                const [statsError, setStatsError] = (0, _react.useState)(null);
                // 获取统计数据
                (0, _react.useEffect)(()=>{
                    const fetchStatsData = async ()=>{
                        try {
                            const stats = await _user.UserService.getUserPersonalStats();
                            setPersonalStats(stats);
                            setStatsError(null);
                        } catch (error) {
                            console.error('获取统计数据失败:', error);
                            setStatsError('获取统计数据失败，请稍后重试');
                        } finally{
                            setStatsLoading(false);
                        }
                    };
                    fetchStatsData();
                }, []);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                    title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            display: 'flex',
                            alignItems: 'center',
                            gap: 8
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.BarChartOutlined, {
                                style: {
                                    fontSize: 16,
                                    color: '#1890ff'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                lineNumber: 81,
                                columnNumber: 11
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                children: "数据概览"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/DataOverview.tsx",
                                lineNumber: 82,
                                columnNumber: 11
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/DataOverview.tsx",
                        lineNumber: 80,
                        columnNumber: 9
                    }, void 0),
                    style: {
                        marginBottom: 16,
                        borderRadius: 8,
                        border: '1px solid #d9d9d9'
                    },
                    headStyle: {
                        borderBottom: '1px solid #f0f0f0',
                        paddingBottom: 12
                    },
                    bodyStyle: {
                        padding: '20px'
                    },
                    children: statsError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                        message: "数据概览加载失败",
                        description: statsError,
                        type: "error",
                        showIcon: true,
                        style: {
                            borderRadius: 8
                        }
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/DataOverview.tsx",
                        lineNumber: 99,
                        columnNumber: 9
                    }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                        spinning: statsLoading,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.StatisticCard.Group, {
                            direction: (screens === null || screens === void 0 ? void 0 : screens.md) ? 'row' : 'column',
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.StatisticCard, {
                                    statistic: {
                                        title: '车辆',
                                        value: personalStats.vehicles,
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CarOutlined, {
                                            style: {
                                                color: '#1890ff',
                                                fontSize: 24
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/DataOverview.tsx",
                                            lineNumber: 117,
                                            columnNumber: 23
                                        }, void 0),
                                        valueStyle: {
                                            color: '#1890ff',
                                            fontSize: 32,
                                            fontWeight: 700
                                        }
                                    },
                                    style: {
                                        borderRadius: 8,
                                        border: '1px solid #d9d9d9',
                                        minHeight: 120
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 113,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.StatisticCard, {
                                    statistic: {
                                        title: '人员',
                                        value: personalStats.personnel,
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UsergroupAddOutlined, {
                                            style: {
                                                color: '#52c41a',
                                                fontSize: 24
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/DataOverview.tsx",
                                            lineNumber: 136,
                                            columnNumber: 23
                                        }, void 0),
                                        valueStyle: {
                                            color: '#52c41a',
                                            fontSize: 32,
                                            fontWeight: 700
                                        }
                                    },
                                    style: {
                                        borderRadius: 8,
                                        border: '1px solid #d9d9d9',
                                        minHeight: 120
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 132,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.StatisticCard, {
                                    statistic: {
                                        title: '预警',
                                        value: personalStats.warnings,
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                            style: {
                                                color: '#faad14',
                                                fontSize: 24
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/DataOverview.tsx",
                                            lineNumber: 155,
                                            columnNumber: 23
                                        }, void 0),
                                        valueStyle: {
                                            color: '#faad14',
                                            fontSize: 32,
                                            fontWeight: 700
                                        }
                                    },
                                    style: {
                                        borderRadius: 8,
                                        border: '1px solid #d9d9d9',
                                        minHeight: 120
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 151,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.StatisticCard, {
                                    statistic: {
                                        title: '告警',
                                        value: personalStats.alerts,
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.AlertOutlined, {
                                            style: {
                                                color: '#ff4d4f',
                                                fontSize: 24
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/DataOverview.tsx",
                                            lineNumber: 174,
                                            columnNumber: 23
                                        }, void 0),
                                        valueStyle: {
                                            color: '#ff4d4f',
                                            fontSize: 32,
                                            fontWeight: 700
                                        }
                                    },
                                    style: {
                                        borderRadius: 8,
                                        border: '1px solid #d9d9d9',
                                        minHeight: 120
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/DataOverview.tsx",
                                    lineNumber: 170,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/DataOverview.tsx",
                            lineNumber: 111,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/DataOverview.tsx",
                        lineNumber: 109,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/DataOverview.tsx",
                    lineNumber: 78,
                    columnNumber: 5
                }, this);
            };
            _s(DataOverview, "1w/0UXDBn4doMzo7UlSafTCdQFw=", true);
            _c = DataOverview;
            var _default = DataOverview;
            var _c;
            $RefreshReg$(_c, "DataOverview");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '6785772156251560671';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/settings/index.tsx": [
            "p__settings__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.16262909773498272916.hot-update.js.map