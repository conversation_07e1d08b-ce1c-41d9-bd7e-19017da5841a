import {
  BarChartOutlined,
  CarOutlined,
  UsergroupAddOutlined,
  ExclamationCircleOutlined,
  AlertOutlined,
} from '@ant-design/icons';
import {
  Alert,
  Col,
  Row,
  Spin,
} from 'antd';
import { ProCard, StatisticCard } from '@ant-design/pro-components';
import React, { useEffect, useState } from 'react';
import { UserService } from '@/services/user';
import type { UserPersonalStatsResponse } from '@/types/api';

/**
 * 数据概览卡片组件
 *
 * 显示用户的个人统计数据，采用单行四列的水平布局。
 * 包括车辆、人员、预警、告警等指标的统计卡片。
 *
 * 主要功能：
 * 1. 显示车辆数量统计
 * 2. 显示人员数量统计
 * 3. 显示预警数量统计
 * 4. 显示告警数量统计
 *
 * 数据来源：
 * - 个人统计数据：通过UserService.getUserPersonalStats()获取
 *
 * 布局特点：
 * - 单行四列水平排列
 * - 每个统计项独立的卡片设计
 * - 响应式布局适配不同屏幕
 */
const DataOverview: React.FC = () => {
  /**
   * 个人统计数据状态管理
   */
  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({
    vehicles: 0,
    personnel: 0,
    warnings: 0,
    alerts: 0,
  });

  const [statsLoading, setStatsLoading] = useState(true);
  const [statsError, setStatsError] = useState<string | null>(null);

  // 获取统计数据
  useEffect(() => {
    const fetchStatsData = async () => {
      try {
        const stats = await UserService.getUserPersonalStats();
        setPersonalStats(stats);
        setStatsError(null);
      } catch (error) {
        console.error('获取统计数据失败:', error);
        setStatsError('获取统计数据失败，请稍后重试');
      } finally {
        setStatsLoading(false);
      }
    };

    fetchStatsData();
  }, []);

  return (
    <ProCard
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <BarChartOutlined style={{ fontSize: 16, color: '#1890ff' }} />
          <span>数据概览</span>
        </div>
      }
      style={{
        marginBottom: 16,
        borderRadius: 8,
        border: '1px solid #d9d9d9',
      }}
      headStyle={{
        borderBottom: '1px solid #f0f0f0',
        paddingBottom: 12,
      }}
      bodyStyle={{
        padding: '20px',
      }}
    >
      {statsError ? (
        <Alert
          message="数据概览加载失败"
          description={statsError}
          type="error"
          showIcon
          style={{
            borderRadius: 8,
          }}
        />
      ) : (
        <Spin spinning={statsLoading}>
          {/* 使用 StatisticCard 组件的响应式网格布局 */}
          <Row gutter={[16, 16]}>
            {/* 车辆统计 */}
            <Col xs={12} sm={6} md={6} lg={6} xl={6}>
              <StatisticCard
                statistic={{
                  title: '车辆',
                  value: personalStats.vehicles,
                  icon: <CarOutlined style={{ color: '#1890ff' }} />,
                  valueStyle: {
                    color: '#1890ff',
                    fontSize: 32,
                    fontWeight: 700,
                  },
                }}
                style={{
                  borderRadius: 8,
                  border: '1px solid #d9d9d9',
                  height: 120,
                }}
              />
            </Col>

            {/* 人员统计 */}
            <Col xs={12} sm={6} md={6} lg={6} xl={6}>
              <StatisticCard
                statistic={{
                  title: '人员',
                  value: personalStats.personnel,
                  icon: <UsergroupAddOutlined style={{ color: '#52c41a' }} />,
                  valueStyle: {
                    color: '#52c41a',
                    fontSize: 32,
                    fontWeight: 700,
                  },
                }}
                style={{
                  borderRadius: 8,
                  border: '1px solid #d9d9d9',
                  height: 120,
                }}
              />
            </Col>

            {/* 预警统计 */}
            <Col xs={12} sm={6} md={6} lg={6} xl={6}>
              <StatisticCard
                statistic={{
                  title: '预警',
                  value: personalStats.warnings,
                  icon: <ExclamationCircleOutlined style={{ color: '#faad14' }} />,
                  valueStyle: {
                    color: '#faad14',
                    fontSize: 32,
                    fontWeight: 700,
                  },
                }}
                style={{
                  borderRadius: 8,
                  border: '1px solid #d9d9d9',
                  height: 120,
                }}
              />
            </Col>

            {/* 告警统计 */}
            <Col xs={12} sm={6} md={6} lg={6} xl={6}>
              <StatisticCard
                statistic={{
                  title: '告警',
                  value: personalStats.alerts,
                  icon: <AlertOutlined style={{ color: '#ff4d4f' }} />,
                  valueStyle: {
                    color: '#ff4d4f',
                    fontSize: 32,
                    fontWeight: 700,
                  },
                }}
                style={{
                  borderRadius: 8,
                  border: '1px solid #d9d9d9',
                  height: 120,
                }}
              />
            </Col>
          </Row>
        </Spin>
      )}
    </ProCard>
  );
};

export default DataOverview;
