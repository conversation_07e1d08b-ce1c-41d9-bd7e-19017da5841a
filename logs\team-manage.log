2025-08-03 00:00:10.040 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-03 00:00:10.042 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-03 00:00:10.043 [http-nio-8080-exec-4] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-03 00:00:10.043 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-03 00:00:10.044 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-03 00:00:10.044 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-03 00:00:10.045 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-03 00:00:10.046 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-03 00:00:10.046 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-03 00:00:10.047 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-03 00:00:10.047 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-03 00:00:10.048 [http-nio-8080-exec-4] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-03 00:00:10.048 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-03 00:00:10.048 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-03 00:00:10.049 [http-nio-8080-exec-4] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-03 00:00:28.475 [http-nio-8080-exec-8] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-03 00:00:28.475 [http-nio-8080-exec-8] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-03 00:00:28.476 [http-nio-8080-exec-8] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-03 00:00:28.477 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-03 00:00:28.477 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-03 00:00:28.477 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-03 00:00:28.478 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-03 00:00:28.478 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-03 00:00:28.478 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-03 00:00:28.478 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-03 00:00:28.479 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-03 00:00:28.479 [http-nio-8080-exec-8] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-03 00:00:28.479 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-03 00:00:28.479 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-03 00:00:28.480 [http-nio-8080-exec-8] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-03 00:01:42.939 [http-nio-8080-exec-2] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-03 00:01:42.939 [http-nio-8080-exec-2] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-03 00:01:42.939 [http-nio-8080-exec-2] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-03 00:01:42.989 [http-nio-8080-exec-1] DEBUG c.t.m.T.findByTeamIdAndAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE team_id = ? AND account_id = ? AND is_deleted = 0
2025-08-03 00:01:42.989 [http-nio-8080-exec-1] DEBUG c.t.m.T.findByTeamIdAndAccountId -- ==> Parameters: 8(Long), 8(Long)
2025-08-03 00:01:42.990 [http-nio-8080-exec-1] DEBUG c.t.m.T.findByTeamIdAndAccountId -- <==      Total: 1
2025-08-03 00:01:42.990 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-03 00:01:42.990 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-03 00:01:42.991 [http-nio-8080-exec-1] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-03 00:01:42.991 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-03 00:01:42.991 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-03 00:01:42.992 [http-nio-8080-exec-1] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-03 00:01:43.368 [http-nio-8080-exec-5] DEBUG c.t.mapper.AccountMapper.selectById -- ==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
2025-08-03 00:01:43.368 [http-nio-8080-exec-7] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-03 00:01:43.368 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-03 00:01:43.369 [http-nio-8080-exec-9] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?)
2025-08-03 00:01:43.369 [http-nio-8080-exec-5] DEBUG c.t.mapper.AccountMapper.selectById -- ==> Parameters: 8(Long)
2025-08-03 00:01:43.369 [http-nio-8080-exec-7] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-03 00:01:43.369 [http-nio-8080-exec-3] DEBUG c.t.mapper.TodoMapper.selectList -- ==>  Preparing: SELECT id,title,description,status,priority,user_id,created_at,updated_at FROM todos WHERE (user_id = ?) ORDER BY created_at DESC
2025-08-03 00:01:43.369 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-03 00:01:43.369 [http-nio-8080-exec-9] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-03 00:01:43.369 [http-nio-8080-exec-3] DEBUG c.t.mapper.TodoMapper.selectList -- ==> Parameters: 8(Long)
2025-08-03 00:01:43.369 [http-nio-8080-exec-5] DEBUG c.t.mapper.AccountMapper.selectById -- <==      Total: 1
2025-08-03 00:01:43.370 [http-nio-8080-exec-7] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-03 00:01:43.370 [http-nio-8080-exec-6] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-03 00:01:43.370 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-03 00:01:43.370 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- ==>  Preparing: SELECT * FROM team_member WHERE account_id = ? AND is_deleted = 0
2025-08-03 00:01:43.370 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-03 00:01:43.370 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-03 00:01:43.370 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- ==> Parameters: 8(Long)
2025-08-03 00:01:43.370 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-03 00:01:43.371 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-03 00:01:43.371 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-03 00:01:43.371 [http-nio-8080-exec-3] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-03 00:01:43.371 [http-nio-8080-exec-5] DEBUG c.t.m.T.findByAccountId -- <==      Total: 2
2025-08-03 00:01:43.371 [http-nio-8080-exec-9] DEBUG c.t.mapper.TodoMapper.selectList -- <==      Total: 6
2025-08-03 00:01:43.371 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-03 00:01:43.371 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-03 00:01:43.371 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-03 00:01:43.372 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-03 00:01:43.372 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-03 00:01:43.372 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 8(Long)
2025-08-03 00:01:43.372 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-03 00:01:43.372 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-03 00:01:43.372 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-03 00:01:43.373 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-03 00:01:43.373 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-03 00:01:43.373 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-03 00:01:43.373 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-03 00:01:43.373 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-03 00:01:43.373 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 8(Long)
2025-08-03 00:01:43.374 [http-nio-8080-exec-6] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-03 00:01:43.374 [http-nio-8080-exec-7] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-03 00:01:43.374 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-03 00:01:43.374 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-03 00:01:43.374 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-03 00:01:43.374 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
2025-08-03 00:01:43.374 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-03 00:01:43.374 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-03 00:01:43.375 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- ==> Parameters: 12(Long)
2025-08-03 00:01:43.375 [http-nio-8080-exec-7] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-03 00:01:43.375 [http-nio-8080-exec-6] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
2025-08-03 00:01:43.375 [http-nio-8080-exec-5] DEBUG c.t.mapper.TeamMapper.selectById -- <==      Total: 1
2025-08-03 00:01:43.376 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
2025-08-03 00:01:43.377 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- ==> Parameters: 12(Long)
2025-08-03 00:01:43.378 [http-nio-8080-exec-5] DEBUG c.t.m.TeamMemberMapper.countByTeamId -- <==      Total: 1
