globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/pages/personal-center/PersonalInfo.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _user = __mako_require__("src/services/user.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            const PersonalInfo = ({ onUserInfoChange })=>{
                _s();
                /**
   * 用户详细信息状态管理
   */ const [userInfo, setUserInfo] = (0, _react.useState)({
                    name: '',
                    position: '',
                    email: '',
                    telephone: '',
                    registerDate: '',
                    lastLoginTime: '',
                    lastLoginTeam: '',
                    teamCount: 0,
                    avatar: ''
                });
                const [userInfoLoading, setUserInfoLoading] = (0, _react.useState)(true);
                const [userInfoError, setUserInfoError] = (0, _react.useState)(null);
                // 获取用户数据
                (0, _react.useEffect)(()=>{
                    const fetchUserData = async ()=>{
                        try {
                            const userDetail = await _user.UserService.getUserProfileDetail();
                            setUserInfo(userDetail);
                            setUserInfoError(null);
                            // 通知父组件用户信息已更新
                            onUserInfoChange === null || onUserInfoChange === void 0 || onUserInfoChange(userDetail);
                        } catch (error) {
                            console.error('获取用户详细信息失败:', error);
                            setUserInfoError('获取用户详细信息失败，请稍后重试');
                        } finally{
                            setUserInfoLoading(false);
                        }
                    };
                    fetchUserData();
                }, [
                    onUserInfoChange
                ]);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                    style: {
                        marginBottom: 16,
                        borderRadius: 8,
                        border: '1px solid #d9d9d9'
                    },
                    children: [
                        userInfoError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                            message: "个人信息加载失败",
                            description: userInfoError,
                            type: "error",
                            showIcon: true,
                            style: {
                                borderRadius: 12,
                                border: 'none'
                            }
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 81,
                            columnNumber: 9
                        }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                            spinning: userInfoLoading,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    padding: '16px 0'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Title, {
                                        level: 3,
                                        style: {
                                            margin: 0,
                                            fontSize: 20,
                                            color: '#262626'
                                        },
                                        children: [
                                            "您好，",
                                            userInfo.name || '加载中...'
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                        lineNumber: 101,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Space, {
                                        size: 16,
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(UserInfoPopover, {
                                                userInfo: userInfo,
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(QuestionCircleOutlined, {
                                                    style: {
                                                        fontSize: 18,
                                                        color: '#8c8c8c',
                                                        cursor: 'pointer',
                                                        transition: 'all 0.2s ease',
                                                        padding: '4px',
                                                        borderRadius: '50%',
                                                        background: 'transparent'
                                                    },
                                                    onMouseEnter: (e)=>{
                                                        e.currentTarget.style.color = '#1890ff';
                                                        e.currentTarget.style.background = 'rgba(24, 144, 255, 0.08)';
                                                        e.currentTarget.style.transform = 'scale(1.1)';
                                                    },
                                                    onMouseLeave: (e)=>{
                                                        e.currentTarget.style.color = '#8c8c8c';
                                                        e.currentTarget.style.background = 'transparent';
                                                        e.currentTarget.style.transform = 'scale(1)';
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                    lineNumber: 115,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                lineNumber: 114,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(SettingOutlined, {
                                                style: {
                                                    fontSize: 18,
                                                    color: '#8c8c8c',
                                                    cursor: 'pointer',
                                                    transition: 'all 0.2s ease',
                                                    padding: '4px',
                                                    borderRadius: '50%',
                                                    background: 'transparent'
                                                },
                                                onMouseEnter: (e)=>{
                                                    e.currentTarget.style.color = '#1890ff';
                                                    e.currentTarget.style.background = 'rgba(24, 144, 255, 0.08)';
                                                    e.currentTarget.style.transform = 'scale(1.1)';
                                                },
                                                onMouseLeave: (e)=>{
                                                    e.currentTarget.style.color = '#8c8c8c';
                                                    e.currentTarget.style.background = 'transparent';
                                                    e.currentTarget.style.transform = 'scale(1)';
                                                },
                                                onClick: ()=>setSettingsModalVisible(true)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                lineNumber: 137,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                        lineNumber: 113,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                lineNumber: 94,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 92,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(UnifiedSettingsModal, {
                            visible: settingsModalVisible,
                            onCancel: ()=>setSettingsModalVisible(false),
                            userInfo: userInfo,
                            onSuccess: ()=>{
                                // 可以在这里刷新用户信息
                                console.log('设置操作成功');
                            }
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 165,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                    lineNumber: 73,
                    columnNumber: 5
                }, this);
            };
            _s(PersonalInfo, "BpsyKZMbaWKljOtpuH57lzgD2Pg=");
            _c = PersonalInfo;
            var _default = PersonalInfo;
            var _c;
            $RefreshReg$(_c, "PersonalInfo");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '10313656570809772979';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/settings/index.tsx": [
            "p__settings__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.15982561628586678866.hot-update.js.map